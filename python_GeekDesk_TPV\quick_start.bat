@echo off
chcp 65001 >nul
title python_GeekDesk_TPV 快速啟動

echo.
echo ========================================
echo   python_GeekDesk_TPV 快速啟動
echo ========================================
echo.

REM 直接嘗試安裝最基本的依賴
echo 正在安裝必要的依賴包...
echo 如果是第一次運行，這可能需要幾分鐘...
echo.

REM 嘗試使用 py 命令
py -m pip install PyQt5 psutil pynput keyboard watchdog Pillow requests PyYAML colorlog python-dateutil >nul 2>&1
if errorlevel 1 (
    REM 如果 py 失敗，嘗試 python
    python -m pip install PyQt5 psutil pynput keyboard watchdog Pillow requests PyYAML colorlog python-dateutil >nul 2>&1
    if errorlevel 1 (
        echo 安裝依賴失敗，嘗試使用國內鏡像源...
        python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple PyQt5 psutil pynput keyboard watchdog Pillow requests PyYAML colorlog python-dateutil
        if errorlevel 1 (
            echo.
            echo 自動安裝失敗，請手動運行以下命令：
            echo pip install PyQt5 psutil pynput keyboard watchdog Pillow requests PyYAML colorlog python-dateutil
            echo.
            pause
            exit /b 1
        )
    )
)

echo 依賴安裝完成！
echo.

REM 啟動程序
echo 啟動 python_GeekDesk_TPV...
echo.

REM 嘗試使用 py 命令啟動
py main.py 2>nul
if errorlevel 1 (
    REM 如果 py 失敗，嘗試 python
    python main.py
    if errorlevel 1 (
        echo.
        echo 程序啟動失敗，請檢查錯誤信息
        pause
    )
)
