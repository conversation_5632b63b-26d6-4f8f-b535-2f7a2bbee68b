#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基礎模型類
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import json
from datetime import datetime


class BaseModel(ABC):
    """基礎模型類"""
    
    def __init__(self, **kwargs):
        self.created_at = kwargs.get('created_at', datetime.now())
        self.updated_at = kwargs.get('updated_at', datetime.now())
        self.id = kwargs.get('id', self._generate_id())
    
    def _generate_id(self) -> str:
        """生成唯一ID"""
        import uuid
        return str(uuid.uuid4())
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseModel':
        """從字典創建實例"""
        pass
    
    def to_json(self) -> str:
        """轉換為JSON字符串"""
        data = self.to_dict()
        # 處理datetime對象
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'BaseModel':
        """從JSON字符串創建實例"""
        data = json.loads(json_str)
        # 處理datetime字符串
        for key, value in data.items():
            if key in ['created_at', 'updated_at'] and isinstance(value, str):
                try:
                    data[key] = datetime.fromisoformat(value)
                except ValueError:
                    data[key] = datetime.now()
        return cls.from_dict(data)
    
    def update(self, **kwargs):
        """更新模型屬性"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now()
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(id={self.id})"
    
    def __repr__(self) -> str:
        return self.__str__()


class ConfigModel(BaseModel):
    """配置模型基類"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = kwargs.get('name', '')
        self.description = kwargs.get('description', '')
        self.enabled = kwargs.get('enabled', True)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'enabled': self.enabled,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigModel':
        return cls(**data)
