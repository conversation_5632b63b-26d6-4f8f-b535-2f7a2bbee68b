// qgeopolygon.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_10_0 -)

class QGeoPolygon : public QGeoShape
{
%TypeHeaderCode
#include <qgeopolygon.h>
%End

public:
    QGeoPolygon();
    QGeoPolygon(const QList<QGeoCoordinate> &path);
    QGeoPolygon(const QGeoPolygon &other);
    QGeoPolygon(const QGeoShape &other);
    ~QGeoPolygon();
    bool operator==(const QGeoPolygon &other) const;
    bool operator!=(const QGeoPolygon &other) const;
    void setPath(const QList<QGeoCoordinate> &path);
    const QList<QGeoCoordinate> &path() const;
    void translate(double degreesLatitude, double degreesLongitude);
    QGeoPolygon translated(double degreesLatitude, double degreesLongitude) const;
    double length(int indexFrom = 0, int indexTo = -1) const;
    int size() const;
    void addCoordinate(const QGeoCoordinate &coordinate);
    void insertCoordinate(int index, const QGeoCoordinate &coordinate);
    void replaceCoordinate(int index, const QGeoCoordinate &coordinate);
    QGeoCoordinate coordinateAt(int index) const;
    bool containsCoordinate(const QGeoCoordinate &coordinate) const;
    void removeCoordinate(const QGeoCoordinate &coordinate);
    void removeCoordinate(int index);
    QString toString() const;
%If (Qt_5_12_0 -)
    void addHole(const QList<QGeoCoordinate> &holePath);
%End
%If (Qt_5_12_0 -)
    void addHole(const QVariant &holePath);
%End
%If (Qt_5_12_0 -)
    const QVariantList hole(int index) const;
%End
%If (Qt_5_12_0 -)
    const QList<QGeoCoordinate> holePath(int index) const;
%End
%If (Qt_5_12_0 -)
    void removeHole(int index);
%End
%If (Qt_5_12_0 -)
    int holesCount() const;
%End

protected:
%If (Qt_5_12_0 -)
    void setPerimeter(const QVariantList &path);
%End
%If (Qt_5_12_0 -)
    QVariantList perimeter() const;
%End
};

%End
