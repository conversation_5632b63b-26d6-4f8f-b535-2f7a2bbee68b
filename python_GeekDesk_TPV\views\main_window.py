#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口
"""

import logging
from typing import Optional, List
from pathlib import Path

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, 
                            QListWidget, QListWidgetItem, QPushButton, QLabel,
                            QFrame, QGraphicsDropShadowEffect, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPoint, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QPainter, QBrush

from utils.config_manager import ConfigManager
from utils.search_manager import SearchManager, SearchResult
from constants.constants import DEFAULT_WINDOW_WIDTH, DEFAULT_WINDOW_HEIGHT
from constants.enums import SearchType


class SearchLineEdit(QLineEdit):
    """自定義搜索輸入框"""
    
    search_requested = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
        # 設置樣式
        self.setPlaceholderText("搜索文件、應用程序...")
        self.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 20px;
                padding: 10px 20px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                outline: none;
            }
        """)
        
        # 搜索延遲定時器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._emit_search)
        
        # 連接信號
        self.textChanged.connect(self._on_text_changed)
        self.returnPressed.connect(self._emit_search)
    
    def _on_text_changed(self, text: str):
        """文本改變事件"""
        # 延遲搜索
        self.search_timer.start(300)
    
    def _emit_search(self):
        """發送搜索信號"""
        text = self.text().strip()
        if text:
            self.search_requested.emit(text)


class SearchResultItem(QListWidgetItem):
    """搜索結果項"""
    
    def __init__(self, result: SearchResult):
        super().__init__()
        
        self.result = result
        self.setText(result.name)
        self.setToolTip(result.path)
        
        # 設置圖標（根據文件類型）
        self._set_icon()
    
    def _set_icon(self):
        """設置圖標"""
        # 這裡可以根據文件類型設置不同的圖標
        # 暫時使用默認圖標
        pass


class SearchResultList(QListWidget):
    """搜索結果列表"""
    
    item_activated = pyqtSignal(SearchResult)
    
    def __init__(self):
        super().__init__()
        
        # 設置樣式
        self.setStyleSheet("""
            QListWidget {
                border: none;
                background-color: transparent;
                outline: none;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                background-color: transparent;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # 連接信號
        self.itemActivated.connect(self._on_item_activated)
        self.itemDoubleClicked.connect(self._on_item_activated)
    
    def _on_item_activated(self, item: QListWidgetItem):
        """項目激活事件"""
        if isinstance(item, SearchResultItem):
            self.item_activated.emit(item.result)
    
    def set_results(self, results: List[SearchResult]):
        """設置搜索結果"""
        self.clear()
        
        for result in results:
            item = SearchResultItem(result)
            self.addItem(item)


class MainWindow(QWidget):
    """主窗口"""
    
    def __init__(self, config_manager: ConfigManager, search_manager: SearchManager):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.search_manager = search_manager
        
        # 窗口屬性
        self.is_animating = False
        self.fade_animation: Optional[QPropertyAnimation] = None
        
        # 初始化UI
        self._init_ui()
        
        # 連接信號
        self._connect_signals()
        
        # 應用配置
        self._apply_config()
    
    def _init_ui(self):
        """初始化UI"""
        # 設置窗口屬性
        self.setWindowFlags(
            Qt.FramelessWindowHint | 
            Qt.WindowStaysOnTopHint | 
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 設置窗口大小
        width = self.config_manager.get_setting("ui.window_width", DEFAULT_WINDOW_WIDTH)
        height = self.config_manager.get_setting("ui.window_height", DEFAULT_WINDOW_HEIGHT)
        self.resize(width, height)
        
        # 創建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(15)
        
        # 創建主容器
        self.main_container = QFrame()
        self.main_container.setObjectName("mainContainer")
        self.main_layout.addWidget(self.main_container)
        
        # 容器布局
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(20, 20, 20, 20)
        container_layout.setSpacing(15)
        
        # 創建搜索框
        self.search_input = SearchLineEdit()
        container_layout.addWidget(self.search_input)
        
        # 創建結果列表
        self.result_list = SearchResultList()
        container_layout.addWidget(self.result_list)
        
        # 創建狀態標籤
        self.status_label = QLabel("輸入關鍵詞開始搜索")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        container_layout.addWidget(self.status_label)
        
        # 設置陰影效果
        self._add_shadow_effect()
        
        # 應用樣式
        self._apply_styles()
    
    def _add_shadow_effect(self):
        """添加陰影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        self.main_container.setGraphicsEffect(shadow)
    
    def _apply_styles(self):
        """應用樣式"""
        # 獲取配置
        opacity = self.config_manager.get_setting("ui.opacity", 0.9)
        corner_radius = self.config_manager.get_setting("ui.corner_radius", 10)
        blur_background = self.config_manager.get_setting("ui.blur_background", True)
        
        # 主容器樣式
        container_style = f"""
            QFrame#mainContainer {{
                background-color: rgba(255, 255, 255, {int(opacity * 255)});
                border-radius: {corner_radius}px;
                border: 1px solid rgba(200, 200, 200, 100);
            }}
        """
        
        self.main_container.setStyleSheet(container_style)
        
        # 窗口透明度
        self.setWindowOpacity(opacity)
    
    def _connect_signals(self):
        """連接信號"""
        # 搜索相關信號
        self.search_input.search_requested.connect(self._on_search_requested)
        self.result_list.item_activated.connect(self._on_result_activated)
        
        # 搜索管理器信號
        self.search_manager.search_started.connect(self._on_search_started)
        self.search_manager.search_results.connect(self._on_search_results)
        self.search_manager.search_finished.connect(self._on_search_finished)
    
    def _apply_config(self):
        """應用配置"""
        # 應用樣式配置
        self._apply_styles()
        
        # 設置字體
        font = QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(10)
        self.setFont(font)
    
    def _on_search_requested(self, query: str):
        """搜索請求事件"""
        self.logger.debug(f"搜索請求: {query}")
        
        # 執行搜索
        search_type = SearchType.EVERYTHING if self.search_manager.is_everything_available() else SearchType.FILE_NAME
        self.search_manager.search(query, search_type)
    
    def _on_search_started(self):
        """搜索開始事件"""
        self.status_label.setText("搜索中...")
        self.result_list.clear()
    
    def _on_search_results(self, results: List[SearchResult]):
        """搜索結果事件"""
        self.result_list.set_results(results)
        
        if results:
            self.status_label.setText(f"找到 {len(results)} 個結果")
        else:
            self.status_label.setText("未找到相關結果")
    
    def _on_search_finished(self):
        """搜索完成事件"""
        pass
    
    def _on_result_activated(self, result: SearchResult):
        """結果激活事件"""
        try:
            import os
            import subprocess
            
            # 打開文件或文件夾
            if result.result_type == "folder":
                os.startfile(result.path)
            else:
                os.startfile(result.path)
            
            # 隱藏窗口
            self.hide()
            
            self.logger.debug(f"打開文件: {result.path}")
            
        except Exception as e:
            self.logger.error(f"打開文件失敗: {e}")
    
    def show_at_cursor(self):
        """在鼠標位置顯示窗口"""
        try:
            # 獲取鼠標位置
            cursor_pos = QApplication.desktop().cursor().pos()
            
            # 計算窗口位置（避免超出屏幕邊界）
            screen_geometry = QApplication.desktop().screenGeometry()
            window_size = self.size()
            
            x = cursor_pos.x() - window_size.width() // 2
            y = cursor_pos.y() - window_size.height() // 2
            
            # 邊界檢查
            if x < 0:
                x = 0
            elif x + window_size.width() > screen_geometry.width():
                x = screen_geometry.width() - window_size.width()
            
            if y < 0:
                y = 0
            elif y + window_size.height() > screen_geometry.height():
                y = screen_geometry.height() - window_size.height()
            
            # 移動窗口
            self.move(x, y)
            
            # 顯示窗口
            self.show()
            self.activateWindow()
            self.raise_()
            
            # 聚焦搜索框
            self.search_input.setFocus()
            self.search_input.selectAll()
            
        except Exception as e:
            self.logger.error(f"在鼠標位置顯示窗口失敗: {e}")
    
    def keyPressEvent(self, event):
        """按鍵事件"""
        if event.key() == Qt.Key_Escape:
            self.hide()
        else:
            super().keyPressEvent(event)
    
    def focusOutEvent(self, event):
        """失去焦點事件"""
        # 延遲隱藏窗口，避免點擊結果時立即隱藏
        QTimer.singleShot(100, self._check_and_hide)
        super().focusOutEvent(event)
    
    def _check_and_hide(self):
        """檢查並隱藏窗口"""
        # 如果窗口或其子控件沒有焦點，則隱藏窗口
        if not self.hasFocus() and not any(child.hasFocus() for child in self.findChildren(QWidget)):
            self.hide()
    
    def showEvent(self, event):
        """顯示事件"""
        super().showEvent(event)
        
        # 播放顯示動畫
        if self.config_manager.get_setting("ui.animation_enabled", True):
            self._play_show_animation()
    
    def _play_show_animation(self):
        """播放顯示動畫"""
        if self.is_animating:
            return
        
        self.is_animating = True
        
        # 淡入動畫
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(200)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(self.config_manager.get_setting("ui.opacity", 0.9))
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        self.fade_animation.finished.connect(lambda: setattr(self, 'is_animating', False))
        self.fade_animation.start()
    
    def closeEvent(self, event):
        """關閉事件"""
        # 隱藏而不是關閉
        event.ignore()
        self.hide()
