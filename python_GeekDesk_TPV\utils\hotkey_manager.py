#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局熱鍵管理器
"""

import logging
from typing import Dict, Callable, Optional, List
import threading

try:
    import keyboard
    KEYBOARD_AVAILABLE = True
except ImportError:
    KEYBOARD_AVAILABLE = False
    logging.warning("keyboard 模塊不可用，熱鍵功能將被禁用")

from utils.config_manager import ConfigManager


class HotkeyManager:
    """全局熱鍵管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 熱鍵註冊表
        self._hotkeys: Dict[str, Dict] = {}
        self._lock = threading.Lock()
        
        # 檢查keyboard模塊可用性
        if not KEYBOARD_AVAILABLE:
            self.logger.warning("keyboard模塊不可用，熱鍵功能已禁用")
    
    def register_hotkey(self, name: str, hotkey: str, callback: Callable, 
                       suppress: bool = True, trigger_on_release: bool = False) -> bool:
        """
        註冊全局熱鍵
        
        Args:
            name: 熱鍵名稱
            hotkey: 熱鍵組合（如 'ctrl+space'）
            callback: 回調函數
            suppress: 是否阻止熱鍵傳遞給其他應用
            trigger_on_release: 是否在釋放時觸發
        
        Returns:
            是否註冊成功
        """
        if not KEYBOARD_AVAILABLE:
            self.logger.warning(f"無法註冊熱鍵 {name}: keyboard模塊不可用")
            return False
        
        try:
            with self._lock:
                # 如果已經註冊，先註銷
                if name in self._hotkeys:
                    self.unregister_hotkey(name)
                
                # 註冊新熱鍵
                hook = keyboard.add_hotkey(
                    hotkey,
                    callback,
                    suppress=suppress,
                    trigger_on_release=trigger_on_release
                )
                
                # 保存註冊信息
                self._hotkeys[name] = {
                    'hotkey': hotkey,
                    'callback': callback,
                    'hook': hook,
                    'suppress': suppress,
                    'trigger_on_release': trigger_on_release
                }
                
                self.logger.info(f"熱鍵 '{name}' ({hotkey}) 註冊成功")
                return True
                
        except Exception as e:
            self.logger.error(f"註冊熱鍵 '{name}' 失敗: {e}")
            return False
    
    def unregister_hotkey(self, name: str) -> bool:
        """
        註銷熱鍵
        
        Args:
            name: 熱鍵名稱
        
        Returns:
            是否註銷成功
        """
        if not KEYBOARD_AVAILABLE:
            return False
        
        try:
            with self._lock:
                if name not in self._hotkeys:
                    self.logger.warning(f"熱鍵 '{name}' 未註冊")
                    return False
                
                # 註銷熱鍵
                hotkey_info = self._hotkeys[name]
                keyboard.remove_hotkey(hotkey_info['hook'])
                
                # 從註冊表中移除
                del self._hotkeys[name]
                
                self.logger.info(f"熱鍵 '{name}' 註銷成功")
                return True
                
        except Exception as e:
            self.logger.error(f"註銷熱鍵 '{name}' 失敗: {e}")
            return False
    
    def unregister_all(self):
        """註銷所有熱鍵"""
        if not KEYBOARD_AVAILABLE:
            return
        
        try:
            with self._lock:
                for name in list(self._hotkeys.keys()):
                    self.unregister_hotkey(name)
                
                self.logger.info("所有熱鍵已註銷")
                
        except Exception as e:
            self.logger.error(f"註銷所有熱鍵失敗: {e}")
    
    def is_registered(self, name: str) -> bool:
        """
        檢查熱鍵是否已註冊
        
        Args:
            name: 熱鍵名稱
        
        Returns:
            是否已註冊
        """
        with self._lock:
            return name in self._hotkeys
    
    def get_registered_hotkeys(self) -> List[Dict]:
        """
        獲取所有已註冊的熱鍵信息
        
        Returns:
            熱鍵信息列表
        """
        with self._lock:
            result = []
            for name, info in self._hotkeys.items():
                result.append({
                    'name': name,
                    'hotkey': info['hotkey'],
                    'suppress': info['suppress'],
                    'trigger_on_release': info['trigger_on_release']
                })
            return result
    
    def update_hotkey(self, name: str, new_hotkey: str) -> bool:
        """
        更新熱鍵組合
        
        Args:
            name: 熱鍵名稱
            new_hotkey: 新的熱鍵組合
        
        Returns:
            是否更新成功
        """
        if not KEYBOARD_AVAILABLE:
            return False
        
        try:
            with self._lock:
                if name not in self._hotkeys:
                    self.logger.warning(f"熱鍵 '{name}' 未註冊")
                    return False
                
                # 獲取原有信息
                old_info = self._hotkeys[name]
                
                # 註銷舊熱鍵
                keyboard.remove_hotkey(old_info['hook'])
                
                # 註冊新熱鍵
                hook = keyboard.add_hotkey(
                    new_hotkey,
                    old_info['callback'],
                    suppress=old_info['suppress'],
                    trigger_on_release=old_info['trigger_on_release']
                )
                
                # 更新註冊信息
                self._hotkeys[name].update({
                    'hotkey': new_hotkey,
                    'hook': hook
                })
                
                self.logger.info(f"熱鍵 '{name}' 已更新為 '{new_hotkey}'")
                return True
                
        except Exception as e:
            self.logger.error(f"更新熱鍵 '{name}' 失敗: {e}")
            return False
    
    def is_hotkey_available(self, hotkey: str) -> bool:
        """
        檢查熱鍵組合是否可用
        
        Args:
            hotkey: 熱鍵組合
        
        Returns:
            是否可用
        """
        if not KEYBOARD_AVAILABLE:
            return False
        
        try:
            # 嘗試解析熱鍵
            keyboard.parse_hotkey(hotkey)
            
            # 檢查是否已被其他熱鍵使用
            with self._lock:
                for info in self._hotkeys.values():
                    if info['hotkey'] == hotkey:
                        return False
            
            return True
            
        except Exception:
            return False
    
    def get_hotkey_info(self, name: str) -> Optional[Dict]:
        """
        獲取熱鍵信息
        
        Args:
            name: 熱鍵名稱
        
        Returns:
            熱鍵信息
        """
        with self._lock:
            if name in self._hotkeys:
                info = self._hotkeys[name].copy()
                # 移除不需要的內部信息
                info.pop('hook', None)
                info.pop('callback', None)
                return info
            return None
    
    @staticmethod
    def normalize_hotkey(hotkey: str) -> str:
        """
        標準化熱鍵字符串
        
        Args:
            hotkey: 原始熱鍵字符串
        
        Returns:
            標準化後的熱鍵字符串
        """
        if not KEYBOARD_AVAILABLE:
            return hotkey
        
        try:
            # 使用keyboard模塊解析並重新格式化
            parsed = keyboard.parse_hotkey(hotkey)
            return keyboard.get_hotkey_name(parsed)
        except Exception:
            return hotkey
    
    @staticmethod
    def is_valid_hotkey(hotkey: str) -> bool:
        """
        檢查熱鍵字符串是否有效
        
        Args:
            hotkey: 熱鍵字符串
        
        Returns:
            是否有效
        """
        if not KEYBOARD_AVAILABLE:
            return False
        
        try:
            keyboard.parse_hotkey(hotkey)
            return True
        except Exception:
            return False
    
    def reload_from_config(self):
        """從配置重新加載熱鍵"""
        if not KEYBOARD_AVAILABLE:
            return
        
        try:
            # 這裡可以根據需要實現從配置文件重新加載熱鍵的邏輯
            self.logger.info("從配置重新加載熱鍵")
            
        except Exception as e:
            self.logger.error(f"從配置重新加載熱鍵失敗: {e}")


# 全局熱鍵管理器實例
_hotkey_manager: Optional[HotkeyManager] = None


def get_hotkey_manager() -> Optional[HotkeyManager]:
    """獲取全局熱鍵管理器實例"""
    return _hotkey_manager


def set_hotkey_manager(manager: HotkeyManager):
    """設置全局熱鍵管理器實例"""
    global _hotkey_manager
    _hotkey_manager = manager
