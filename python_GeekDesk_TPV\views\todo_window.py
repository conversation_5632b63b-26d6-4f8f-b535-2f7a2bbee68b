#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
待辦事項窗口
"""

import logging
from typing import List, Optional
from datetime import datetime, timedelta

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, 
                            QListWidgetItem, QPushButton, QLineEdit, QTextEdit,
                            QDateTimeEdit, QComboBox, QCheckBox, QLabel, QGroupBox,
                            QFormLayout, QMessageBox, QSplitter, QWidget, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal, QDateTime, QTimer
from PyQt5.QtGui import QFont, QIcon

from utils.config_manager import ConfigManager
from models.todo_item import TodoItem
from constants.enums import TodoTaskExecType


class TodoItemWidget(QWidget):
    """待辦事項控件"""
    
    item_changed = pyqtSignal(TodoItem)
    item_deleted = pyqtSignal(TodoItem)
    
    def __init__(self, todo_item: TodoItem):
        super().__init__()
        self.todo_item = todo_item
        self._init_ui()
        self._update_display()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 完成狀態複選框
        self.completed_cb = QCheckBox()
        self.completed_cb.toggled.connect(self._on_completed_changed)
        layout.addWidget(self.completed_cb)
        
        # 標題標籤
        self.title_label = QLabel()
        self.title_label.setWordWrap(True)
        layout.addWidget(self.title_label, 1)
        
        # 優先級標籤
        self.priority_label = QLabel()
        self.priority_label.setFixedWidth(50)
        layout.addWidget(self.priority_label)
        
        # 狀態標籤
        self.status_label = QLabel()
        self.status_label.setFixedWidth(80)
        layout.addWidget(self.status_label)
        
        # 刪除按鈕
        self.delete_btn = QPushButton("刪除")
        self.delete_btn.setFixedWidth(60)
        self.delete_btn.clicked.connect(lambda: self.item_deleted.emit(self.todo_item))
        layout.addWidget(self.delete_btn)
    
    def _update_display(self):
        """更新顯示"""
        # 設置完成狀態
        self.completed_cb.setChecked(self.todo_item.completed)
        
        # 設置標題
        title = self.todo_item.title
        if self.todo_item.completed:
            title = f"<s>{title}</s>"
        self.title_label.setText(title)
        
        # 設置優先級
        priority_colors = {
            1: "#4CAF50",  # 綠色
            2: "#8BC34A",  # 淺綠
            3: "#FFC107",  # 黃色
            4: "#FF9800",  # 橙色
            5: "#F44336"   # 紅色
        }
        priority_color = priority_colors.get(self.todo_item.priority, "#9E9E9E")
        self.priority_label.setText(self.todo_item.get_priority_text())
        self.priority_label.setStyleSheet(f"color: {priority_color}; font-weight: bold;")
        
        # 設置狀態
        status_text = self.todo_item.get_status_text()
        status_colors = {
            "已完成": "#4CAF50",
            "已過期": "#F44336",
            "今天到期": "#FF9800",
            "即將到期": "#FFC107",
            "進行中": "#2196F3"
        }
        status_color = status_colors.get(status_text, "#9E9E9E")
        self.status_label.setText(status_text)
        self.status_label.setStyleSheet(f"color: {status_color}; font-weight: bold;")
    
    def _on_completed_changed(self, checked: bool):
        """完成狀態改變"""
        if checked:
            self.todo_item.mark_completed()
        else:
            self.todo_item.mark_uncompleted()
        
        self._update_display()
        self.item_changed.emit(self.todo_item)


class TodoEditDialog(QDialog):
    """待辦事項編輯對話框"""
    
    def __init__(self, todo_item: Optional[TodoItem] = None, parent=None):
        super().__init__(parent)
        
        self.todo_item = todo_item or TodoItem()
        self.is_new = todo_item is None
        
        self._init_ui()
        self._load_data()
    
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("編輯待辦事項" if not self.is_new else "新建待辦事項")
        self.setFixedSize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # 基本信息組
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        # 標題
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("請輸入待辦事項標題")
        basic_layout.addRow("標題:", self.title_edit)
        
        # 內容
        self.content_edit = QTextEdit()
        self.content_edit.setMaximumHeight(100)
        self.content_edit.setPlaceholderText("請輸入詳細描述（可選）")
        basic_layout.addRow("描述:", self.content_edit)
        
        # 優先級
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["很低", "低", "中", "高", "很高"])
        basic_layout.addRow("優先級:", self.priority_combo)
        
        # 分類
        self.category_edit = QLineEdit()
        self.category_edit.setPlaceholderText("分類（可選）")
        basic_layout.addRow("分類:", self.category_edit)
        
        layout.addWidget(basic_group)
        
        # 時間設置組
        time_group = QGroupBox("時間設置")
        time_layout = QFormLayout(time_group)
        
        # 到期時間
        self.due_date_edit = QDateTimeEdit()
        self.due_date_edit.setDateTime(QDateTime.currentDateTime().addDays(1))
        self.due_date_edit.setCalendarPopup(True)
        time_layout.addRow("到期時間:", self.due_date_edit)
        
        # 提醒時間
        self.reminder_time_edit = QDateTimeEdit()
        self.reminder_time_edit.setDateTime(QDateTime.currentDateTime().addHours(1))
        self.reminder_time_edit.setCalendarPopup(True)
        time_layout.addRow("提醒時間:", self.reminder_time_edit)
        
        layout.addWidget(time_group)
        
        # 重複設置組
        repeat_group = QGroupBox("重複設置")
        repeat_layout = QFormLayout(repeat_group)
        
        # 重複類型
        self.exec_type_combo = QComboBox()
        self.exec_type_combo.addItems(["一次性", "每日", "每週", "每月", "每年"])
        repeat_layout.addRow("重複類型:", self.exec_type_combo)
        
        # 重複間隔
        self.repeat_interval_spin = QSpinBox()
        self.repeat_interval_spin.setRange(1, 365)
        self.repeat_interval_spin.setValue(1)
        repeat_layout.addRow("重複間隔:", self.repeat_interval_spin)
        
        layout.addWidget(repeat_group)
        
        # 提醒設置組
        reminder_group = QGroupBox("提醒設置")
        reminder_layout = QFormLayout(reminder_group)
        
        # 啟用提醒
        self.reminder_enabled_cb = QCheckBox("啟用提醒")
        self.reminder_enabled_cb.setChecked(True)
        reminder_layout.addRow(self.reminder_enabled_cb)
        
        # 提醒聲音
        self.reminder_sound_cb = QCheckBox("提醒聲音")
        self.reminder_sound_cb.setChecked(True)
        reminder_layout.addRow(self.reminder_sound_cb)
        
        # 彈窗提醒
        self.reminder_popup_cb = QCheckBox("彈窗提醒")
        self.reminder_popup_cb.setChecked(True)
        reminder_layout.addRow(self.reminder_popup_cb)
        
        layout.addWidget(reminder_group)
        
        # 按鈕布局
        button_layout = QHBoxLayout()
        
        # 取消按鈕
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        # 確定按鈕
        self.ok_btn = QPushButton("確定")
        self.ok_btn.clicked.connect(self._save_and_close)
        self.ok_btn.setDefault(True)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
    
    def _load_data(self):
        """加載數據"""
        if not self.is_new:
            self.title_edit.setText(self.todo_item.title)
            self.content_edit.setPlainText(self.todo_item.content)
            self.priority_combo.setCurrentIndex(self.todo_item.priority - 1)
            self.category_edit.setText(self.todo_item.category)
            
            if self.todo_item.due_date:
                self.due_date_edit.setDateTime(QDateTime.fromSecsSinceEpoch(int(self.todo_item.due_date.timestamp())))
            
            if self.todo_item.reminder_time:
                self.reminder_time_edit.setDateTime(QDateTime.fromSecsSinceEpoch(int(self.todo_item.reminder_time.timestamp())))
            
            self.exec_type_combo.setCurrentIndex(self.todo_item.exec_type.value)
            self.repeat_interval_spin.setValue(self.todo_item.repeat_interval)
            
            self.reminder_enabled_cb.setChecked(self.todo_item.reminder_enabled)
            self.reminder_sound_cb.setChecked(self.todo_item.reminder_sound)
            self.reminder_popup_cb.setChecked(self.todo_item.reminder_popup)
    
    def _save_and_close(self):
        """保存並關閉"""
        # 驗證輸入
        if not self.title_edit.text().strip():
            QMessageBox.warning(self, "警告", "請輸入標題")
            return
        
        # 更新待辦事項
        self.todo_item.title = self.title_edit.text().strip()
        self.todo_item.content = self.content_edit.toPlainText().strip()
        self.todo_item.priority = self.priority_combo.currentIndex() + 1
        self.todo_item.category = self.category_edit.text().strip() or "default"
        
        self.todo_item.due_date = self.due_date_edit.dateTime().toPyDateTime()
        self.todo_item.reminder_time = self.reminder_time_edit.dateTime().toPyDateTime()
        
        self.todo_item.exec_type = TodoTaskExecType(self.exec_type_combo.currentIndex())
        self.todo_item.repeat_interval = self.repeat_interval_spin.value()
        
        self.todo_item.reminder_enabled = self.reminder_enabled_cb.isChecked()
        self.todo_item.reminder_sound = self.reminder_sound_cb.isChecked()
        self.todo_item.reminder_popup = self.reminder_popup_cb.isChecked()
        
        self.accept()
    
    def get_todo_item(self) -> TodoItem:
        """獲取待辦事項"""
        return self.todo_item


class TodoWindow(QDialog):
    """待辦事項窗口"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 待辦事項列表
        self.todo_items: List[TodoItem] = []
        
        # 初始化UI
        self._init_ui()
        
        # 加載數據
        self._load_todos()
        
        # 設置定時器檢查提醒
        self.reminder_timer = QTimer()
        self.reminder_timer.timeout.connect(self._check_reminders)
        self.reminder_timer.start(60000)  # 每分鐘檢查一次
    
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("待辦事項")
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 工具欄
        toolbar_layout = QHBoxLayout()
        
        # 新建按鈕
        self.new_btn = QPushButton("新建")
        self.new_btn.clicked.connect(self._new_todo)
        toolbar_layout.addWidget(self.new_btn)
        
        # 編輯按鈕
        self.edit_btn = QPushButton("編輯")
        self.edit_btn.clicked.connect(self._edit_todo)
        self.edit_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_btn)
        
        # 刪除按鈕
        self.delete_btn = QPushButton("刪除")
        self.delete_btn.clicked.connect(self._delete_todo)
        self.delete_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_btn)
        
        toolbar_layout.addStretch()
        
        # 篩選組合框
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "進行中", "已完成", "已過期", "今天到期"])
        self.filter_combo.currentTextChanged.connect(self._filter_todos)
        toolbar_layout.addWidget(QLabel("篩選:"))
        toolbar_layout.addWidget(self.filter_combo)
        
        layout.addLayout(toolbar_layout)
        
        # 待辦事項列表
        self.todo_list = QListWidget()
        self.todo_list.itemSelectionChanged.connect(self._on_selection_changed)
        self.todo_list.itemDoubleClicked.connect(self._edit_todo)
        layout.addWidget(self.todo_list)
        
        # 設置字體
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
    
    def _load_todos(self):
        """加載待辦事項"""
        # 這裡應該從數據庫或文件加載
        # 暫時創建一些示例數據
        self.todo_items = [
            TodoItem(
                title="完成項目報告",
                content="需要完成季度項目總結報告",
                priority=4,
                due_date=datetime.now() + timedelta(days=2),
                reminder_time=datetime.now() + timedelta(hours=1)
            ),
            TodoItem(
                title="購買生活用品",
                content="牙膏、洗髮水、衛生紙",
                priority=2,
                due_date=datetime.now() + timedelta(days=1),
                reminder_time=datetime.now() + timedelta(hours=2)
            )
        ]
        
        self._update_todo_list()
    
    def _update_todo_list(self):
        """更新待辦事項列表"""
        self.todo_list.clear()
        
        # 根據篩選條件過濾
        filtered_items = self._get_filtered_items()
        
        for todo_item in filtered_items:
            # 創建列表項
            list_item = QListWidgetItem()
            self.todo_list.addItem(list_item)
            
            # 創建自定義控件
            todo_widget = TodoItemWidget(todo_item)
            todo_widget.item_changed.connect(self._on_todo_changed)
            todo_widget.item_deleted.connect(self._on_todo_deleted)
            
            # 設置控件到列表項
            list_item.setSizeHint(todo_widget.sizeHint())
            self.todo_list.setItemWidget(list_item, todo_widget)
    
    def _get_filtered_items(self) -> List[TodoItem]:
        """獲取過濾後的項目"""
        filter_text = self.filter_combo.currentText()
        
        if filter_text == "全部":
            return self.todo_items
        elif filter_text == "進行中":
            return [item for item in self.todo_items if not item.completed]
        elif filter_text == "已完成":
            return [item for item in self.todo_items if item.completed]
        elif filter_text == "已過期":
            return [item for item in self.todo_items if item.is_overdue()]
        elif filter_text == "今天到期":
            return [item for item in self.todo_items if item.is_due_today()]
        
        return self.todo_items
    
    def _on_selection_changed(self):
        """選擇改變事件"""
        has_selection = bool(self.todo_list.selectedItems())
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
    
    def _new_todo(self):
        """新建待辦事項"""
        dialog = TodoEditDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            todo_item = dialog.get_todo_item()
            self.todo_items.append(todo_item)
            self._update_todo_list()
            self._save_todos()
    
    def _edit_todo(self):
        """編輯待辦事項"""
        current_item = self.todo_list.currentItem()
        if not current_item:
            return
        
        # 獲取對應的待辦事項
        todo_widget = self.todo_list.itemWidget(current_item)
        if not isinstance(todo_widget, TodoItemWidget):
            return
        
        dialog = TodoEditDialog(todo_widget.todo_item, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self._update_todo_list()
            self._save_todos()
    
    def _delete_todo(self):
        """刪除待辦事項"""
        current_item = self.todo_list.currentItem()
        if not current_item:
            return
        
        reply = QMessageBox.question(
            self,
            "確認刪除",
            "確定要刪除選中的待辦事項嗎？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            todo_widget = self.todo_list.itemWidget(current_item)
            if isinstance(todo_widget, TodoItemWidget):
                self._on_todo_deleted(todo_widget.todo_item)
    
    def _on_todo_changed(self, todo_item: TodoItem):
        """待辦事項改變事件"""
        self._save_todos()
    
    def _on_todo_deleted(self, todo_item: TodoItem):
        """待辦事項刪除事件"""
        if todo_item in self.todo_items:
            self.todo_items.remove(todo_item)
            self._update_todo_list()
            self._save_todos()
    
    def _filter_todos(self):
        """篩選待辦事項"""
        self._update_todo_list()
    
    def _check_reminders(self):
        """檢查提醒"""
        for todo_item in self.todo_items:
            if todo_item.should_remind():
                self._show_reminder(todo_item)
                todo_item.remind()
    
    def _show_reminder(self, todo_item: TodoItem):
        """顯示提醒"""
        if todo_item.reminder_popup:
            QMessageBox.information(
                self,
                "待辦提醒",
                f"提醒：{todo_item.title}\n\n{todo_item.content}"
            )
        
        # 這裡可以添加聲音提醒
        if todo_item.reminder_sound:
            # 播放提醒聲音
            pass
    
    def _save_todos(self):
        """保存待辦事項"""
        # 這裡應該保存到數據庫或文件
        # 暫時只記錄日誌
        self.logger.debug(f"保存 {len(self.todo_items)} 個待辦事項")
