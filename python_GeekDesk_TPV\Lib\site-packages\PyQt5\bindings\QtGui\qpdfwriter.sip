// qpdfwriter.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPdfWriter : public QObject, public QPagedPaintDevice
{
%TypeHeaderCode
#include <qpdfwriter.h>
%End

public:
    explicit QPdfWriter(const QString &filename);
    explicit QPdfWriter(QIODevice *device);
    virtual ~QPdfWriter();
    QString title() const;
    void setTitle(const QString &title);
    QString creator() const;
    void setCreator(const QString &creator);
    virtual bool newPage();
    virtual void setPageSize(QPagedPaintDevice::PageSize size);
    bool setPageSize(const QPageSize &pageSize);
    virtual void setPageSizeMM(const QSizeF &size);
    virtual void setMargins(const QPagedPaintDevice::Margins &m);

protected:
    virtual QPaintEngine *paintEngine() const;
    virtual int metric(QPaintDevice::PaintDeviceMetric id) const;

public:
%If (Qt_5_3_0 -)
    void setResolution(int resolution);
%End
%If (Qt_5_3_0 -)
    int resolution() const;
%End
%If (Qt_5_10_0 -)
    void setPdfVersion(QPagedPaintDevice::PdfVersion version);
%End
%If (Qt_5_10_0 -)
    QPagedPaintDevice::PdfVersion pdfVersion() const;
%End
%If (Qt_5_15_0 -)
    void setDocumentXmpMetadata(const QByteArray &xmpMetadata);
%End
%If (Qt_5_15_0 -)
    QByteArray documentXmpMetadata() const;
%End
%If (Qt_5_15_0 -)
    void addFileAttachment(const QString &fileName, const QByteArray &data, const QString &mimeType = QString());
%End

private:
    QPdfWriter(const QPdfWriter &);
};
