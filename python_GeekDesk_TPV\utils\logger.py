#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日誌管理模塊
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

import colorlog

from constants.constants import LOGS_DIR, LOG_FILE, ERROR_LOG_FILE, APP_NAME


def setup_logger(
    name: Optional[str] = None,
    level: int = logging.INFO,
    console_output: bool = True,
    file_output: bool = True,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    設置日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
        level: 日誌級別
        console_output: 是否輸出到控制台
        file_output: 是否輸出到文件
        max_file_size: 最大文件大小（字節）
        backup_count: 備份文件數量
    
    Returns:
        配置好的日誌記錄器
    """
    
    # 創建日誌記錄器
    logger_name = name or APP_NAME
    logger = logging.getLogger(logger_name)
    
    # 如果已經配置過，直接返回
    if logger.handlers:
        return logger
    
    logger.setLevel(level)
    
    # 創建格式化器
    file_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = colorlog.ColoredFormatter(
        fmt='%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s%(reset)s',
        datefmt='%H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # 控制台處理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # 文件處理器
    if file_output:
        # 確保日誌目錄存在
        LOGS_DIR.mkdir(parents=True, exist_ok=True)
        
        # 普通日誌文件
        file_handler = logging.handlers.RotatingFileHandler(
            filename=LOG_FILE,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # 錯誤日誌文件
        error_handler = logging.handlers.RotatingFileHandler(
            filename=ERROR_LOG_FILE,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        logger.addHandler(error_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    獲取日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
    
    Returns:
        日誌記錄器
    """
    return logging.getLogger(name)


class LoggerMixin:
    """日誌記錄器混入類"""
    
    @property
    def logger(self) -> logging.Logger:
        """獲取日誌記錄器"""
        if not hasattr(self, '_logger'):
            self._logger = get_logger(self.__class__.__name__)
        return self._logger


def log_exception(logger: logging.Logger, message: str = "發生異常"):
    """
    記錄異常信息的裝飾器
    
    Args:
        logger: 日誌記錄器
        message: 異常消息
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"{message}: {e}", exc_info=True)
                raise
        return wrapper
    return decorator


def setup_file_logger(
    name: str,
    file_path: Path,
    level: int = logging.INFO,
    max_file_size: int = 10 * 1024 * 1024,
    backup_count: int = 5
) -> logging.Logger:
    """
    設置文件日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
        file_path: 日誌文件路徑
        level: 日誌級別
        max_file_size: 最大文件大小
        backup_count: 備份文件數量
    
    Returns:
        配置好的日誌記錄器
    """
    logger = logging.getLogger(name)
    
    if logger.handlers:
        return logger
    
    logger.setLevel(level)
    
    # 確保目錄存在
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 創建文件處理器
    handler = logging.handlers.RotatingFileHandler(
        filename=file_path,
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    
    # 創建格式化器
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger


def clear_old_logs(days: int = 30):
    """
    清理舊日誌文件
    
    Args:
        days: 保留天數
    """
    import time
    from datetime import datetime, timedelta
    
    if not LOGS_DIR.exists():
        return
    
    cutoff_time = time.time() - (days * 24 * 60 * 60)
    
    for log_file in LOGS_DIR.glob("*.log*"):
        try:
            if log_file.stat().st_mtime < cutoff_time:
                log_file.unlink()
                print(f"已刪除舊日誌文件: {log_file}")
        except Exception as e:
            print(f"刪除日誌文件失敗 {log_file}: {e}")


# 設置默認日誌記錄器
default_logger = setup_logger()
