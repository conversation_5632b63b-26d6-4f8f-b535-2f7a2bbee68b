// qsslsocket.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_SSL)

class QSslSocket : public QTcpSocket
{
%TypeHeaderCode
#include <qsslsocket.h>
%End

public:
    enum SslMode
    {
        UnencryptedMode,
        SslClientMode,
        SslServerMode,
    };

    explicit QSslSocket(QObject *parent /TransferThis/ = 0);
    virtual ~QSslSocket();
    void connectToHostEncrypted(const QString &hostName, quint16 port, QIODevice::OpenMode mode = QIODevice::ReadWrite, QAbstractSocket::NetworkLayerProtocol protocol = QAbstractSocket::AnyIPProtocol) /ReleaseGIL/;
    void connectToHostEncrypted(const QString &hostName, quint16 port, const QString &sslPeerName, QIODevice::OpenMode mode = QIODevice::ReadWrite, QAbstractSocket::NetworkLayerProtocol protocol = QAbstractSocket::AnyIPProtocol) /ReleaseGIL/;
    virtual bool setSocketDescriptor(qintptr socketDescriptor, QAbstractSocket::SocketState state = QAbstractSocket::ConnectedState, QIODevice::OpenMode mode = QIODevice::ReadWrite);
    QSslSocket::SslMode mode() const;
    bool isEncrypted() const;
    QSsl::SslProtocol protocol() const;
    void setProtocol(QSsl::SslProtocol protocol);
    virtual qint64 bytesAvailable() const;
    virtual qint64 bytesToWrite() const;
    virtual bool canReadLine() const;
    virtual void close();
    virtual bool atEnd() const;
    bool flush();
    void abort();
    void setLocalCertificate(const QSslCertificate &certificate);
    void setLocalCertificate(const QString &path, QSsl::EncodingFormat format = QSsl::Pem);
    QSslCertificate localCertificate() const;
    QSslCertificate peerCertificate() const;
    QList<QSslCertificate> peerCertificateChain() const;
    QSslCipher sessionCipher() const;
    void setPrivateKey(const QSslKey &key);
    void setPrivateKey(const QString &fileName, QSsl::KeyAlgorithm algorithm = QSsl::Rsa, QSsl::EncodingFormat format = QSsl::Pem, const QByteArray &passPhrase = QByteArray());
    QSslKey privateKey() const;
    QList<QSslCipher> ciphers() const;
    void setCiphers(const QList<QSslCipher> &ciphers);
    void setCiphers(const QString &ciphers);
    static void setDefaultCiphers(const QList<QSslCipher> &ciphers);
    static QList<QSslCipher> defaultCiphers();
    static QList<QSslCipher> supportedCiphers();
    bool addCaCertificates(const QString &path, QSsl::EncodingFormat format = QSsl::Pem, QRegExp::PatternSyntax syntax = QRegExp::FixedString);
    void addCaCertificate(const QSslCertificate &certificate);
    void addCaCertificates(const QList<QSslCertificate> &certificates);
    void setCaCertificates(const QList<QSslCertificate> &certificates);
    QList<QSslCertificate> caCertificates() const;
    static bool addDefaultCaCertificates(const QString &path, QSsl::EncodingFormat format = QSsl::Pem, QRegExp::PatternSyntax syntax = QRegExp::FixedString);
    static void addDefaultCaCertificate(const QSslCertificate &certificate);
    static void addDefaultCaCertificates(const QList<QSslCertificate> &certificates);
    static void setDefaultCaCertificates(const QList<QSslCertificate> &certificates);
    static QList<QSslCertificate> defaultCaCertificates();
    static QList<QSslCertificate> systemCaCertificates();
    virtual bool waitForConnected(int msecs = 30000) /ReleaseGIL/;
    bool waitForEncrypted(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForReadyRead(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForBytesWritten(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForDisconnected(int msecs = 30000) /ReleaseGIL/;
    QList<QSslError> sslErrors() const;
    static bool supportsSsl();

public slots:
    void startClientEncryption();
    void startServerEncryption();
    void ignoreSslErrors();

signals:
    void encrypted();
    void sslErrors(const QList<QSslError> &errors);
    void modeChanged(QSslSocket::SslMode newMode);
%If (Qt_5_5_0 -)
    void preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator *authenticator);
%End

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QSslSocket::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 len /ArraySize/) /ReleaseGIL/;

public:
    enum PeerVerifyMode
    {
        VerifyNone,
        QueryPeer,
        VerifyPeer,
        AutoVerifyPeer,
    };

    QSslSocket::PeerVerifyMode peerVerifyMode() const;
    void setPeerVerifyMode(QSslSocket::PeerVerifyMode mode);
    int peerVerifyDepth() const;
    void setPeerVerifyDepth(int depth);
    virtual void setReadBufferSize(qint64 size);
    qint64 encryptedBytesAvailable() const;
    qint64 encryptedBytesToWrite() const;
    QSslConfiguration sslConfiguration() const;
    void setSslConfiguration(const QSslConfiguration &config);

signals:
    void peerVerifyError(const QSslError &error);
    void encryptedBytesWritten(qint64 totalBytes);

public:
    virtual void setSocketOption(QAbstractSocket::SocketOption option, const QVariant &value);
    virtual QVariant socketOption(QAbstractSocket::SocketOption option);
    void ignoreSslErrors(const QList<QSslError> &errors);
    QString peerVerifyName() const;
    void setPeerVerifyName(const QString &hostName);
    virtual void resume() /ReleaseGIL/;
    virtual void connectToHost(const QString &hostName, quint16 port, QIODevice::OpenMode mode = QIODevice::ReadWrite, QAbstractSocket::NetworkLayerProtocol protocol = QAbstractSocket::AnyIPProtocol) /ReleaseGIL/;
    virtual void disconnectFromHost() /ReleaseGIL/;
    static long sslLibraryVersionNumber();
    static QString sslLibraryVersionString();
%If (Qt_5_1_0 -)
    void setLocalCertificateChain(const QList<QSslCertificate> &localChain);
%End
%If (Qt_5_1_0 -)
    QList<QSslCertificate> localCertificateChain() const;
%End
%If (Qt_5_4_0 -)
    QSsl::SslProtocol sessionProtocol() const;
%End
%If (Qt_5_4_0 -)
    static long sslLibraryBuildVersionNumber();
%End
%If (Qt_5_4_0 -)
    static QString sslLibraryBuildVersionString();
%End
%If (Qt_5_13_0 -)
    QVector<QOcspResponse> ocspResponses() const;
%End
%If (Qt_5_15_0 -)
    QList<QSslError> sslHandshakeErrors() const;
%End

signals:
%If (Qt_5_15_0 -)
    void newSessionTicketReceived();
%End
};

%End
