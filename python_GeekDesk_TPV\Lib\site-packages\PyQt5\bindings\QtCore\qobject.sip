// qobject.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


typedef QList<QObject *> QObjectList;

class QObject /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qobject.h>
%End

%TypeCode
// This is needed by the tr() handwritten implementation.
#include <qcoreapplication.h>


// These are the helper functions for QObject::findChild() and
// QObject::findChildren.

// Wrap the given type in a 1-tuple.
static PyObject *qtcore_type_to_tuple(PyObject *type)
{
    PyObject *tuple = PyTuple_New(1);

    if (tuple)
    {
        Py_INCREF(type);
        PyTuple_SetItem(tuple, 0, type);
    }

    return tuple;
}


// Check all elements of a given tuple are type objects and return a new
// reference to the tuple if so.
static PyObject *qtcore_check_tuple_types(PyObject *types)
{
    for (Py_ssize_t i = 0; i < PyTuple_Size(types); ++i)
        if (!PyObject_TypeCheck(PyTuple_GetItem(types, i), &PyType_Type))
        {
            PyErr_SetString(PyExc_TypeError,
                    "all elements of the types argument must be type objects");
            return 0;
        }

    Py_INCREF(types);
    return types;
}


// Do the main work of finding a child.
static PyObject *qtcore_do_find_child(const QObject *parent, PyObject *types, const QString &name, Qt::FindChildOptions options)
{
    const QObjectList &children = parent->children();
    int i;

    for (i = 0; i < children.size(); ++i)
    {
        QObject *obj = children.at(i);
        PyObject *pyo = sipConvertFromType(obj, sipType_QObject, 0);

        if (!pyo)
            return 0;

        // Allow for proxies.
        QObject *resolved = reinterpret_cast<QObject *>(sipGetAddress((sipSimpleWrapper *)pyo));

        if (name.isNull() || resolved->objectName() == name)
            for (Py_ssize_t t = 0; t < PyTuple_Size(types); ++t)
                if (PyType_IsSubtype(Py_TYPE(pyo), (PyTypeObject *)PyTuple_GetItem(types, t)))
                    return pyo;

        Py_DECREF(pyo);
    }

    if (options == Qt::FindChildrenRecursively)
        for (i = 0; i < children.size(); ++i)
        {
            PyObject *pyo = qtcore_do_find_child(children.at(i), types, name, options);

            if (pyo != Py_None)
                return pyo;

            Py_DECREF(pyo);
        }

    Py_INCREF(Py_None);
    return Py_None;
}


// Find a child that is one of a number of types and with an optional name.
static PyObject *qtcore_FindChild(const QObject *parent, PyObject *types, const QString &name, Qt::FindChildOptions options)
{
    // Check that the types checking was successful.
    if (!types)
        return 0;

    PyObject *child = qtcore_do_find_child(parent, types, name, options);

    Py_DECREF(types);

    return child;
}


// Do the main work of finding the children with a string name.
static bool qtcore_do_find_children(const QObject *parent, PyObject *types, const QString &name, Qt::FindChildOptions options, PyObject *list)
{
    const QObjectList &children = parent->children();
    int i;

    for (i = 0; i < children.size(); ++i)
    {
        QObject *obj = children.at(i);
        PyObject *pyo = sipConvertFromType(obj, sipType_QObject, 0);

        if (!pyo)
            return false;

        // Allow for proxies.
        QObject *resolved = reinterpret_cast<QObject *>(sipGetAddress((sipSimpleWrapper *)pyo));

        if (name.isNull() || resolved->objectName() == name)
            for (Py_ssize_t t = 0; t < PyTuple_Size(types); ++t)
                if (PyType_IsSubtype(Py_TYPE(pyo), (PyTypeObject *)PyTuple_GetItem(types, t)))
                    if (PyList_Append(list, pyo) < 0)
                    {
                        Py_DECREF(pyo);
                        return false;
                    }

        Py_DECREF(pyo);

        if (options == Qt::FindChildrenRecursively)
        {
            bool ok = qtcore_do_find_children(obj, types, name, options, list);

            if (!ok)
                return false;
        }
    }

    return true;
}


// Find a child that is one of a number of types and with an optional string
// name.
static PyObject *qtcore_FindChildren(const QObject *parent, PyObject *types, const QString &name, Qt::FindChildOptions options)
{
    // Check that the types checking was successful.
    if (!types)
        return 0;

    PyObject *list = PyList_New(0);

    if (list)
        if (!qtcore_do_find_children(parent, types, name, options, list))
            Py_DECREF(list);

    Py_DECREF(types);

    return list;
}


// Do the main work of finding the children with a QRegExp name.
static bool qtcore_do_find_children(const QObject *parent, PyObject *types, const QRegExp &re, Qt::FindChildOptions options, PyObject *list)
{
    const QObjectList &children = parent->children();
    int i;

    for (i = 0; i < children.size(); ++i)
    {
        QObject *obj = children.at(i);
        PyObject *pyo = sipConvertFromType(obj, sipType_QObject, 0);

        if (!pyo)
            return false;

        if (re.indexIn(obj->objectName()) >= 0)
            for (Py_ssize_t t = 0; t < PyTuple_Size(types); ++t)
                if (PyType_IsSubtype(Py_TYPE(pyo), (PyTypeObject *)PyTuple_GetItem(types, t)))
                    if (PyList_Append(list, pyo) < 0)
                    {
                        Py_DECREF(pyo);
                        return false;
                    }

        Py_DECREF(pyo);

        if (options == Qt::FindChildrenRecursively)
        {
            bool ok = qtcore_do_find_children(obj, types, re, options, list);

            if (!ok)
                return false;
        }
    }

    return true;
}


// Find a child that is one of a number of types and with an optional QRegExp
// name.
static PyObject *qtcore_FindChildren(const QObject *parent, PyObject *types, const QRegExp &re, Qt::FindChildOptions options)
{
    // Check that the types checking was successful.
    if (!types)
        return 0;

    PyObject *list = PyList_New(0);

    if (list)
        if (!qtcore_do_find_children(parent, types, re, options, list))
            Py_DECREF(list);

    Py_DECREF(types);

    return list;
}


// Do the main work of finding the children with a QRegularExpression name.
static bool qtcore_do_find_children(const QObject *parent, PyObject *types, const QRegularExpression &re, Qt::FindChildOptions options, PyObject *list)
{
    const QObjectList &children = parent->children();
    int i;

    for (i = 0; i < children.size(); ++i)
    {
        QObject *obj = children.at(i);
        PyObject *pyo = sipConvertFromType(obj, sipType_QObject, 0);

        if (!pyo)
            return false;

        QRegularExpressionMatch m = re.match(obj->objectName());
        
        if (m.hasMatch())
            for (Py_ssize_t t = 0; t < PyTuple_Size(types); ++t)
                if (PyType_IsSubtype(Py_TYPE(pyo), (PyTypeObject *)PyTuple_GetItem(types, t)))
                    if (PyList_Append(list, pyo) < 0)
                    {
                        Py_DECREF(pyo);
                        return false;
                    }

        Py_DECREF(pyo);

        if (options == Qt::FindChildrenRecursively)
        {
            bool ok = qtcore_do_find_children(obj, types, re, options, list);

            if (!ok)
                return false;
        }
    }

    return true;
}


// Find a child that is one of a number of types and with an optional
// QRegularExpression name.
static PyObject *qtcore_FindChildren(const QObject *parent, PyObject *types, const QRegularExpression &re, Qt::FindChildOptions options)
{
    // Check that the types checking was successful.
    if (!types)
        return 0;

    PyObject *list = PyList_New(0);

    if (list)
        if (!qtcore_do_find_children(parent, types, re, options, list))
            Py_DECREF(list);

    Py_DECREF(types);

    return list;
}
%End

%FinalisationCode
    return qpycore_qobject_finalisation(sipSelf, sipCpp, sipKwds, sipUnused);
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QAbstractAnimation, &sipType_QAbstractAnimation, 25, 1},
        {sipName_QAbstractEventDispatcher, &sipType_QAbstractEventDispatcher, -1, 2},
        {sipName_QAbstractItemModel, &sipType_QAbstractItemModel, 31, 3},
        {sipName_QAbstractState, &sipType_QAbstractState, 39, 4},
        {sipName_QAbstractTransition, &sipType_QAbstractTransition, 43, 5},
        {sipName_QIODevice, &sipType_QIODevice, 45, 6},
        {sipName_QCoreApplication, &sipType_QCoreApplication, -1, 7},
        {sipName_QEventLoop, &sipType_QEventLoop, -1, 8},
    #if QT_VERSION >= 0x050200
        {sipName_QFileSelector, &sipType_QFileSelector, -1, 9},
    #else
        {0, 0, -1, 9},
    #endif
        {sipName_QFileSystemWatcher, &sipType_QFileSystemWatcher, -1, 10},
        {sipName_QItemSelectionModel, &sipType_QItemSelectionModel, -1, 11},
        {sipName_QLibrary, &sipType_QLibrary, -1, 12},
        {sipName_QMimeData, &sipType_QMimeData, -1, 13},
        {sipName_QObjectCleanupHandler, &sipType_QObjectCleanupHandler, -1, 14},
        {sipName_QPluginLoader, &sipType_QPluginLoader, -1, 15},
        {sipName_QSettings, &sipType_QSettings, -1, 16},
        {sipName_QSharedMemory, &sipType_QSharedMemory, -1, 17},
        {sipName_QSignalMapper, &sipType_QSignalMapper, -1, 18},
        {sipName_QSocketNotifier, &sipType_QSocketNotifier, -1, 19},
        {sipName_QThread, &sipType_QThread, -1, 20},
        {sipName_QThreadPool, &sipType_QThreadPool, -1, 21},
        {sipName_QTimeLine, &sipType_QTimeLine, -1, 22},
        {sipName_QTimer, &sipType_QTimer, -1, 23},
        {sipName_QTranslator, &sipType_QTranslator, -1, 24},
    #if defined(Q_OS_WIN)
        {sipName_QWinEventNotifier, &sipType_QWinEventNotifier, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
        {sipName_QAnimationGroup, &sipType_QAnimationGroup, 28, 26},
        {sipName_QPauseAnimation, &sipType_QPauseAnimation, -1, 27},
        {sipName_QVariantAnimation, &sipType_QVariantAnimation, 30, -1},
        {sipName_QParallelAnimationGroup, &sipType_QParallelAnimationGroup, -1, 29},
        {sipName_QSequentialAnimationGroup, &sipType_QSequentialAnimationGroup, -1, -1},
        {sipName_QPropertyAnimation, &sipType_QPropertyAnimation, -1, -1},
        {sipName_QAbstractListModel, &sipType_QAbstractListModel, 35, 32},
        {sipName_QAbstractProxyModel, &sipType_QAbstractProxyModel, 36, 33},
        {sipName_QAbstractTableModel, &sipType_QAbstractTableModel, -1, 34},
    #if QT_VERSION >= 0x050d00
        {sipName_QConcatenateTablesProxyModel, &sipType_QConcatenateTablesProxyModel, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
        {sipName_QStringListModel, &sipType_QStringListModel, -1, -1},
        {sipName_QIdentityProxyModel, &sipType_QIdentityProxyModel, -1, 37},
        {sipName_QSortFilterProxyModel, &sipType_QSortFilterProxyModel, -1, 38},
    #if QT_VERSION >= 0x050d00
        {sipName_QTransposeProxyModel, &sipType_QTransposeProxyModel, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
        {sipName_QFinalState, &sipType_QFinalState, -1, 40},
        {sipName_QHistoryState, &sipType_QHistoryState, -1, 41},
        {sipName_QState, &sipType_QState, 42, -1},
        {sipName_QStateMachine, &sipType_QStateMachine, -1, -1},
        {sipName_QEventTransition, &sipType_QEventTransition, -1, 44},
        {sipName_QSignalTransition, &sipType_QSignalTransition, -1, -1},
        {sipName_QBuffer, &sipType_QBuffer, -1, 46},
        {sipName_QFileDevice, &sipType_QFileDevice, 48, 47},
    #if !defined(QT_NO_PROCESS)
        {sipName_QProcess, &sipType_QProcess, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
        {sipName_QFile, &sipType_QFile, 50, 49},
    #if QT_VERSION >= 0x050100
        {sipName_QSaveFile, &sipType_QSaveFile, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
        {sipName_QTemporaryFile, &sipType_QTemporaryFile, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

%GCTraverseCode
    // Traverse any saved slots we might be connected to.
    sipRes = qpycore_visitSlotProxies(sipCpp, sipVisit, sipArg);
%End

%GCClearCode
    // Clear any saved slots we might be connected to.
    sipRes = qpycore_clearSlotProxies(sipCpp);
%End

public:
    static const QMetaObject staticMetaObject {
%GetCode
        sipPy = qpycore_qobject_staticmetaobject(sipPyType);
%End

    };
    const QMetaObject *metaObject() const;
    explicit QObject(QObject *parent /TransferThis/ = 0);
    virtual ~QObject();
    void pyqtConfigure(SIP_PYOBJECT) /NoArgParser/;
%Docstring
QObject.pyqtConfigure(...)

Each keyword argument is either the name of a Qt property or a Qt signal.
For properties the property is set to the given value which should be of an
appropriate type.
For signals the signal is connected to the given value which should be a
callable.
%End

%MethodCode
        return qpycore_pyqtconfigure(sipSelf, sipArgs, sipKwds);
%End

    SIP_PYOBJECT __getattr__(const char *name /Encoding="UTF-8"/) const /NoTypeHint/;
%MethodCode
        sipRes = qpycore_qobject_getattr(sipCpp, sipSelf, a0);
%End

    virtual bool event(QEvent *);
    virtual bool eventFilter(QObject *, QEvent *);
    QString tr(const char *sourceText /Encoding="UTF-8"/, const char *disambiguation = 0, int n = -1) const;
%MethodCode
        // Note that tr() is really a static method.  We pretend it isn't so we can use
        // self to get hold of the class name.
        
        sipRes = new QString(QCoreApplication::translate(sipPyTypeName(Py_TYPE(sipSelf)), a0, a1, a2));
%End

    SIP_PYOBJECT findChild(SIP_PYTYPE type /TypeHint="Type[QObjectT]"/, const QString &name = QString(), Qt::FindChildOptions options = Qt::FindChildrenRecursively) const /TypeHint="QObjectT"/;
%MethodCode
        sipRes = qtcore_FindChild(sipCpp, qtcore_type_to_tuple(a0), *a1, *a2);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYOBJECT findChild(SIP_PYTUPLE types /TypeHint="Tuple[Type[QObjectT], ...]", TypeHintValue="()"/, const QString &name = QString(), Qt::FindChildOptions options = Qt::FindChildrenRecursively) const /TypeHint="QObjectT"/;
%MethodCode
        sipRes = qtcore_FindChild(sipCpp, qtcore_check_tuple_types(a0), *a1, *a2);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYLIST findChildren(SIP_PYTYPE type /TypeHint="Type[QObjectT]"/, const QString &name = QString(), Qt::FindChildOptions options = Qt::FindChildrenRecursively) const /TypeHint="List[QObjectT]"/;
%MethodCode
        sipRes = qtcore_FindChildren(sipCpp, qtcore_type_to_tuple(a0), *a1, *a2);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYLIST findChildren(SIP_PYTUPLE types /TypeHint="Tuple[Type[QObjectT], ...]", TypeHintValue="()"/, const QString &name = QString(), Qt::FindChildOptions options = Qt::FindChildrenRecursively) const /TypeHint="List[QObjectT]"/;
%MethodCode
        sipRes = qtcore_FindChildren(sipCpp, qtcore_check_tuple_types(a0), *a1, *a2);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYLIST findChildren(SIP_PYTYPE type /TypeHint="Type[QObjectT]"/, const QRegExp &regExp, Qt::FindChildOptions options = Qt::FindChildrenRecursively) const /TypeHint="List[QObjectT]"/;
%MethodCode
        sipRes = qtcore_FindChildren(sipCpp, qtcore_type_to_tuple(a0), *a1, *a2);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYLIST findChildren(SIP_PYTUPLE types /TypeHint="Tuple[Type[QObjectT], ...]", TypeHintValue="()"/, const QRegExp &regExp, Qt::FindChildOptions options = Qt::FindChildrenRecursively) const /TypeHint="List[QObjectT]"/;
%MethodCode
        sipRes = qtcore_FindChildren(sipCpp, qtcore_check_tuple_types(a0), *a1, *a2);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYLIST findChildren(SIP_PYTYPE type /TypeHint="Type[QObjectT]"/, const QRegularExpression &re, Qt::FindChildOptions options = Qt::FindChildrenRecursively) const /TypeHint="List[QObjectT]"/;
%MethodCode
        sipRes = qtcore_FindChildren(sipCpp, qtcore_type_to_tuple(a0), *a1, *a2);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYLIST findChildren(SIP_PYTUPLE types /TypeHint="Tuple[Type[QObjectT], ...]", TypeHintValue="()"/, const QRegularExpression &re, Qt::FindChildOptions options = Qt::FindChildrenRecursively) const /TypeHint="List[QObjectT]"/;
%MethodCode
        sipRes = qtcore_FindChildren(sipCpp, qtcore_check_tuple_types(a0), *a1, *a2);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    QString objectName() const;
    void setObjectName(const QString &name);
    bool isWidgetType() const;
    bool isWindowType() const;
    bool signalsBlocked() const;
    bool blockSignals(bool b);
    QThread *thread() const;
    void moveToThread(QThread *thread);
    int startTimer(int interval, Qt::TimerType timerType = Qt::CoarseTimer);
    void killTimer(int id);
    const QObjectList &children() const;
    void setParent(QObject * /TransferThis/);
    void installEventFilter(QObject *);
    void removeEventFilter(QObject *);
%If (Qt_5_9_0 -)
    void dumpObjectInfo() const;
%End
%If (- Qt_5_9_0)
    void dumpObjectInfo();
%End
%If (Qt_5_9_0 -)
    void dumpObjectTree() const;
%End
%If (- Qt_5_9_0)
    void dumpObjectTree();
%End
    QList<QByteArray> dynamicPropertyNames() const;
    bool setProperty(const char *name, const QVariant &value);
    QVariant property(const char *name) const;

signals:
    void destroyed(QObject *object = 0);
    void objectNameChanged(const QString &objectName);

public:
    QObject *parent() const;
    bool inherits(const char *classname) const;

public slots:
    void deleteLater() /TransferThis/;

protected:
    QObject *sender() const /ReleaseGIL/;
%MethodCode
        // sender() must be called without the GIL to avoid possible deadlocks between
        // the GIL and Qt's internal thread data mutex.
        
        Py_BEGIN_ALLOW_THREADS
        
        #if defined(SIP_PROTECTED_IS_PUBLIC)
        sipRes = sipCpp->sender();
        #else
        sipRes = sipCpp->sipProtect_sender();
        #endif
        
        Py_END_ALLOW_THREADS
        
        if (!sipRes)
        {
            typedef QObject *(*qtcore_qobject_sender_t)();
        
            static qtcore_qobject_sender_t qtcore_qobject_sender = 0;
        
            if (!qtcore_qobject_sender)
            {
                qtcore_qobject_sender = (qtcore_qobject_sender_t)sipImportSymbol("qtcore_qobject_sender");
                Q_ASSERT(qtcore_qobject_sender);
            }
        
            sipRes = qtcore_qobject_sender();
        }
%End

    int receivers(SIP_PYOBJECT signal /TypeHint="PYQT_SIGNAL"/) const [int (const char *signal)];
%MethodCode
        // We need to handle the signal object.  Import the helper if it hasn't already
        // been done.
        typedef sipErrorState (*pyqt5_get_signal_signature_t)(PyObject *, const QObject *, const QByteArray &);
        
        static pyqt5_get_signal_signature_t pyqt5_get_signal_signature = 0;
        
        if (!pyqt5_get_signal_signature)
        {
            pyqt5_get_signal_signature = (pyqt5_get_signal_signature_t)sipImportSymbol("pyqt5_get_signal_signature");
            Q_ASSERT(pyqt5_get_signal_signature);
        }
        
        QByteArray signal_signature;
            
        #if defined(SIP_PROTECTED_IS_PUBLIC)
        if ((sipError = pyqt5_get_signal_signature(a0, sipCpp, signal_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->receivers(signal_signature.constData());
        }
        #else
        if ((sipError = pyqt5_get_signal_signature(a0, static_cast<const QObject *>(sipCpp), signal_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->sipProtect_receivers(signal_signature.constData());
        }
        #endif
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    virtual void timerEvent(QTimerEvent *);
    virtual void childEvent(QChildEvent *);
    virtual void customEvent(QEvent *);
    virtual void connectNotify(const QMetaMethod &signal);
    virtual void disconnectNotify(const QMetaMethod &signal);
    int senderSignalIndex() const;
    bool isSignalConnected(const QMetaMethod &signal) const;

public:
    static bool disconnect(const QMetaObject::Connection &);
    SIP_PYOBJECT disconnect() const /TypeHint=""/;
%MethodCode
        sipRes = qpycore_qobject_disconnect(sipCpp);
%End

private:
    QObject(const QObject &);
};

SIP_PYOBJECT Q_CLASSINFO(const char *name, const char *value) /TypeHint=""/;
%MethodCode
    sipRes = qpycore_ClassInfo(a0, a1);
%End

SIP_PYOBJECT Q_ENUM(SIP_PYOBJECT /TypeHint="Union[type, enum.Enum]"/) /TypeHint=""/;
%MethodCode
    sipRes = qpycore_Enum(a0);
%End

SIP_PYOBJECT Q_ENUMS(...) /TypeHint=""/;
%MethodCode
    sipRes = qpycore_Enums(a0);
%End

SIP_PYOBJECT Q_FLAG(SIP_PYOBJECT /TypeHint="Union[type, enum.Enum]"/) /TypeHint=""/;
%MethodCode
    sipRes = qpycore_Flag(a0);
%End

SIP_PYOBJECT Q_FLAGS(...) /TypeHint=""/;
%MethodCode
    sipRes = qpycore_Flags(a0);
%End

SIP_PYOBJECT QT_TR_NOOP(SIP_PYOBJECT /TypeHint="str"/) /TypeHint="str"/;
%MethodCode
    Py_INCREF(a0);
    sipRes = a0;
%End

SIP_PYOBJECT QT_TR_NOOP_UTF8(SIP_PYOBJECT /TypeHint="str"/) /TypeHint="str"/;
%MethodCode
    Py_INCREF(a0);
    sipRes = a0;
%End

SIP_PYOBJECT QT_TRANSLATE_NOOP(SIP_PYOBJECT /TypeHint="str"/, SIP_PYOBJECT /TypeHint="str"/) /TypeHint="str"/;
%MethodCode
    Py_INCREF(a1);
    sipRes = a1;
%End

SIP_PYOBJECT pyqtSlot(... types, const char *name = 0, const char *result = 0) /NoArgParser, NoTypeHint/;
%Docstring
@pyqtSlot(*types, name: Optional[str], result: Optional[str])

This is a decorator applied to Python methods of a QObject that marks them
as Qt slots.
The non-keyword arguments are the types of the slot arguments and each may
be a Python type object or a string specifying a C++ type.
name is the name of the slot and defaults to the name of the method.
result is type of the value returned by the slot.
%End

%MethodCode
    return qpycore_pyqtslot(sipArgs, sipKwds);
%End

%If (Qt_5_3_0 -)

class QSignalBlocker
{
%TypeHeaderCode
#include <qobject.h>
%End

public:
    explicit QSignalBlocker(QObject *o);
    ~QSignalBlocker();
    void reblock();
    void unblock();
    SIP_PYOBJECT __enter__();
%MethodCode
        // Just return a reference to self.
        sipRes = sipSelf;
        Py_INCREF(sipRes);
%End

    void __exit__(SIP_PYOBJECT type, SIP_PYOBJECT value, SIP_PYOBJECT traceback);
%MethodCode
        sipCpp->unblock();
%End

private:
    QSignalBlocker(const QSignalBlocker &);
};

%End

%ModuleHeaderCode
#include "qpycore_api.h"
%End

%ModuleCode
// Disable the (supposedly) compulsory parts of the Qt support API.
#define sipQtCreateUniversalSlot    0
#define sipQtDestroyUniversalSlot   0
#define sipQtFindSlot               0
#define sipQtConnect                0
#define sipQtDisconnect             0
#define sipQtSameSignalSlotName     0
#define sipQtFindSipslot            0
%End

%PreInitialisationCode
#if defined(Q_OS_DARWIN)
    // This works around a problem (possibly a clash between Qt and Python)
    // began with Qt v5.11 that causes missed paint events.  Only set the
    // variable if it hasn't already been given a value.
    if (qgetenv("QT_MAC_WANTS_LAYER").isNull())
        qputenv("QT_MAC_WANTS_LAYER", "1");
#endif
%End

%InitialisationCode
qpycore_init();
%End

%PostInitialisationCode
qpycore_post_init(sipModuleDict);
%End
