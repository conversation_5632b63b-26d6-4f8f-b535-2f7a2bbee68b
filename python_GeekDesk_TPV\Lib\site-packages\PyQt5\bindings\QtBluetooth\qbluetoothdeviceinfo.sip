// qbluetoothdeviceinfo.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QBluetoothDeviceInfo
{
%TypeHeaderCode
#include <qbluetoothdeviceinfo.h>
%End

public:
    enum MajorDeviceClass
    {
        MiscellaneousDevice,
        ComputerDevice,
        PhoneDevice,
        LANAccessDevice,
%If (Qt_5_13_0 -)
        NetworkDevice,
%End
        AudioVideoDevice,
        PeripheralDevice,
        ImagingDevice,
        WearableDevice,
        ToyDevice,
        HealthDevice,
        UncategorizedDevice,
    };

    enum MinorMiscellaneousClass
    {
        UncategorizedMiscellaneous,
    };

    enum MinorComputerClass
    {
        UncategorizedComputer,
        DesktopComputer,
        ServerComputer,
        LaptopComputer,
        HandheldClamShellComputer,
        HandheldComputer,
        WearableComputer,
    };

    enum MinorPhoneClass
    {
        UncategorizedPhone,
        CellularPhone,
        CordlessPhone,
        SmartPhone,
        WiredModemOrVoiceGatewayPhone,
        CommonIsdnAccessPhone,
    };

    enum MinorNetworkClass
    {
        NetworkFullService,
        NetworkLoadFactorOne,
        NetworkLoadFactorTwo,
        NetworkLoadFactorThree,
        NetworkLoadFactorFour,
        NetworkLoadFactorFive,
        NetworkLoadFactorSix,
        NetworkNoService,
    };

    enum MinorAudioVideoClass
    {
        UncategorizedAudioVideoDevice,
        WearableHeadsetDevice,
        HandsFreeDevice,
        Microphone,
        Loudspeaker,
        Headphones,
        PortableAudioDevice,
        CarAudio,
        SetTopBox,
        HiFiAudioDevice,
        Vcr,
        VideoCamera,
        Camcorder,
        VideoMonitor,
        VideoDisplayAndLoudspeaker,
        VideoConferencing,
        GamingDevice,
    };

    enum MinorPeripheralClass
    {
        UncategorizedPeripheral,
        KeyboardPeripheral,
        PointingDevicePeripheral,
        KeyboardWithPointingDevicePeripheral,
        JoystickPeripheral,
        GamepadPeripheral,
        RemoteControlPeripheral,
        SensingDevicePeripheral,
        DigitizerTabletPeripheral,
        CardReaderPeripheral,
    };

    enum MinorImagingClass
    {
        UncategorizedImagingDevice,
        ImageDisplay,
        ImageCamera,
        ImageScanner,
        ImagePrinter,
    };

    enum MinorWearableClass
    {
        UncategorizedWearableDevice,
        WearableWristWatch,
        WearablePager,
        WearableJacket,
        WearableHelmet,
        WearableGlasses,
    };

    enum MinorToyClass
    {
        UncategorizedToy,
        ToyRobot,
        ToyVehicle,
        ToyDoll,
        ToyController,
        ToyGame,
    };

    enum MinorHealthClass
    {
        UncategorizedHealthDevice,
        HealthBloodPressureMonitor,
        HealthThermometer,
        HealthWeightScale,
        HealthGlucoseMeter,
        HealthPulseOximeter,
        HealthDataDisplay,
        HealthStepCounter,
    };

    enum ServiceClass
    {
        NoService,
        PositioningService,
        NetworkingService,
        RenderingService,
        CapturingService,
        ObjectTransferService,
        AudioService,
        TelephonyService,
        InformationService,
        AllServices,
    };

    typedef QFlags<QBluetoothDeviceInfo::ServiceClass> ServiceClasses;

    enum DataCompleteness
    {
        DataComplete,
        DataIncomplete,
        DataUnavailable,
    };

    QBluetoothDeviceInfo();
    QBluetoothDeviceInfo(const QBluetoothAddress &address, const QString &name, quint32 classOfDevice);
%If (Qt_5_5_0 -)
    QBluetoothDeviceInfo(const QBluetoothUuid &uuid, const QString &name, quint32 classOfDevice);
%End
    QBluetoothDeviceInfo(const QBluetoothDeviceInfo &other);
    ~QBluetoothDeviceInfo();
    bool isValid() const;
    bool isCached() const;
    void setCached(bool cached);
    bool operator==(const QBluetoothDeviceInfo &other) const;
    bool operator!=(const QBluetoothDeviceInfo &other) const;
    QBluetoothAddress address() const;
    QString name() const;
    QBluetoothDeviceInfo::ServiceClasses serviceClasses() const;
    QBluetoothDeviceInfo::MajorDeviceClass majorDeviceClass() const;
    quint8 minorDeviceClass() const;
    qint16 rssi() const;
    void setRssi(qint16 signal);
    void setServiceUuids(const QList<QBluetoothUuid> &uuids, QBluetoothDeviceInfo::DataCompleteness completeness);
%If (Qt_5_13_0 -)
    void setServiceUuids(const QVector<QBluetoothUuid> &uuids);
%End
    QList<QBluetoothUuid> serviceUuids(QBluetoothDeviceInfo::DataCompleteness *completeness /Out/ = 0) const;
    QBluetoothDeviceInfo::DataCompleteness serviceUuidsCompleteness() const;
%If (Qt_5_4_0 -)

    enum CoreConfiguration
    {
        UnknownCoreConfiguration,
        LowEnergyCoreConfiguration,
        BaseRateCoreConfiguration,
        BaseRateAndLowEnergyCoreConfiguration,
    };

%End
%If (Qt_5_4_0 -)
    typedef QFlags<QBluetoothDeviceInfo::CoreConfiguration> CoreConfigurations;
%End
%If (Qt_5_4_0 -)
    void setCoreConfigurations(QBluetoothDeviceInfo::CoreConfigurations coreConfigs);
%End
%If (Qt_5_4_0 -)
    QBluetoothDeviceInfo::CoreConfigurations coreConfigurations() const;
%End
%If (Qt_5_5_0 -)
    void setDeviceUuid(const QBluetoothUuid &uuid);
%End
%If (Qt_5_5_0 -)
    QBluetoothUuid deviceUuid() const;
%End
%If (Qt_5_12_0 -)

    enum class Field
    {
        None /PyName=None_/,
        RSSI,
        ManufacturerData,
        All,
    };

%End
%If (Qt_5_12_0 -)
    typedef QFlags<QBluetoothDeviceInfo::Field> Fields;
%End
%If (Qt_5_12_0 -)
    QVector<quint16> manufacturerIds() const;
%End
%If (Qt_5_12_0 -)
    QByteArray manufacturerData(quint16 manufacturerId) const;
%End
%If (Qt_5_12_0 -)
    bool setManufacturerData(quint16 manufacturerId, const QByteArray &data);
%End
%If (Qt_5_12_0 -)
    QHash<quint16, QByteArray> manufacturerData() const;
%End
};

%End
%If (Qt_5_5_0 -)
QFlags<QBluetoothDeviceInfo::CoreConfiguration> operator|(QBluetoothDeviceInfo::CoreConfiguration f1, QFlags<QBluetoothDeviceInfo::CoreConfiguration> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QBluetoothDeviceInfo::ServiceClass> operator|(QBluetoothDeviceInfo::ServiceClass f1, QFlags<QBluetoothDeviceInfo::ServiceClass> f2);
%End
