@echo off
chcp 65001 >nul
title 安裝 python_GeekDesk_TPV

echo.
echo ========================================
echo   python_GeekDesk_TPV 安裝程序
echo ========================================
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo 錯誤: 未找到 Python
        echo.
        echo 請先安裝 Python 3.8 或更高版本
        echo 下載地址: https://www.python.org/downloads/
        echo.
        echo 安裝 Python 時請確保勾選 "Add Python to PATH"
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

echo 找到 Python: 
%PYTHON_CMD% --version
echo.

REM 檢查 pip
%PYTHON_CMD% -m pip --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: pip 不可用
    echo 請重新安裝 Python 並確保包含 pip
    pause
    exit /b 1
)

REM 創建虛擬環境
echo 步驟 1/4: 創建虛擬環境...
if exist "venv" (
    echo 虛擬環境已存在，跳過創建
) else (
    %PYTHON_CMD% -m venv venv
    if errorlevel 1 (
        echo 錯誤: 創建虛擬環境失敗
        pause
        exit /b 1
    )
    echo 虛擬環境創建成功
)

REM 激活虛擬環境
echo.
echo 步驟 2/4: 激活虛擬環境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo 錯誤: 激活虛擬環境失敗
    pause
    exit /b 1
)

REM 升級 pip
echo.
echo 步驟 3/4: 升級 pip...
python -m pip install --upgrade pip

REM 安裝依賴
echo.
echo 步驟 4/4: 安裝依賴包...
echo 這可能需要幾分鐘時間，請耐心等待...
echo.

REM 首先嘗試默認源
pip install -r requirements.txt
if errorlevel 1 (
    echo.
    echo 默認源安裝失敗，嘗試使用清華大學鏡像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    if errorlevel 1 (
        echo.
        echo 清華源也失敗，嘗試使用阿里雲鏡像源...
        pip install -i https://mirrors.aliyun.com/pypi/simple -r requirements.txt
        if errorlevel 1 (
            echo.
            echo 所有鏡像源都失敗，請檢查網絡連接
            echo 您也可以手動安裝依賴：
            echo   pip install PyQt5 psutil pynput keyboard watchdog Pillow requests PyYAML colorlog
            pause
            exit /b 1
        )
    )
)

echo.
echo ========================================
echo   安裝完成！
echo ========================================
echo.
echo 現在您可以：
echo 1. 運行 start.bat 啟動程序
echo 2. 或者運行: python main.py
echo.
echo 默認熱鍵: Ctrl+Space (呼出主面板)
echo.
pause
