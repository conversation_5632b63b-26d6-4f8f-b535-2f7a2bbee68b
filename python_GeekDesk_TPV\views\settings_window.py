#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
設置窗口
"""

import logging
from typing import Dict, Any

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, 
                            QWidget, QLabel, QLineEdit, QSpinBox, QDoubleSpinBox,
                            QCheckBox, QComboBox, QPushButton, QGroupBox, QSlider,
                            QFormLayout, QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from utils.config_manager import ConfigManager


class GeneralSettingsTab(QWidget):
    """常規設置標籤頁"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 應用程序設置組
        app_group = QGroupBox("應用程序設置")
        app_layout = QFormLayout(app_group)
        
        # 開機自啟動
        self.auto_start_cb = QCheckBox("開機自動啟動")
        app_layout.addRow(self.auto_start_cb)
        
        # 最小化到托盤
        self.minimize_to_tray_cb = QCheckBox("最小化到系統托盤")
        app_layout.addRow(self.minimize_to_tray_cb)
        
        # 關閉到托盤
        self.close_to_tray_cb = QCheckBox("關閉時隱藏到托盤")
        app_layout.addRow(self.close_to_tray_cb)
        
        layout.addWidget(app_group)
        
        # 界面設置組
        ui_group = QGroupBox("界面設置")
        ui_layout = QFormLayout(ui_group)
        
        # 主題
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["自動", "淺色", "深色"])
        ui_layout.addRow("主題:", self.theme_combo)
        
        # 透明度
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(30, 100)
        self.opacity_slider.setValue(90)
        self.opacity_label = QLabel("90%")
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        ui_layout.addRow("透明度:", opacity_layout)
        
        # 圓角半徑
        self.corner_radius_spin = QSpinBox()
        self.corner_radius_spin.setRange(0, 50)
        self.corner_radius_spin.setSuffix(" px")
        ui_layout.addRow("圓角半徑:", self.corner_radius_spin)
        
        # 跟隨鼠標
        self.follow_mouse_cb = QCheckBox("面板跟隨鼠標位置")
        ui_layout.addRow(self.follow_mouse_cb)
        
        # 啟用動畫
        self.animation_cb = QCheckBox("啟用界面動畫")
        ui_layout.addRow(self.animation_cb)
        
        # 毛玻璃效果
        self.blur_background_cb = QCheckBox("啟用背景模糊效果")
        ui_layout.addRow(self.blur_background_cb)
        
        layout.addWidget(ui_group)
        
        # 連接信號
        self.opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%")
        )
    
    def _load_settings(self):
        """加載設置"""
        # 應用程序設置
        self.auto_start_cb.setChecked(
            self.config_manager.get_setting("app.auto_start", False)
        )
        self.minimize_to_tray_cb.setChecked(
            self.config_manager.get_setting("app.minimize_to_tray", True)
        )
        self.close_to_tray_cb.setChecked(
            self.config_manager.get_setting("app.close_to_tray", True)
        )
        
        # 界面設置
        theme = self.config_manager.get_setting("ui.theme", "auto")
        theme_index = {"auto": 0, "light": 1, "dark": 2}.get(theme, 0)
        self.theme_combo.setCurrentIndex(theme_index)
        
        opacity = int(self.config_manager.get_setting("ui.opacity", 0.9) * 100)
        self.opacity_slider.setValue(opacity)
        self.opacity_label.setText(f"{opacity}%")
        
        self.corner_radius_spin.setValue(
            self.config_manager.get_setting("ui.corner_radius", 10)
        )
        self.follow_mouse_cb.setChecked(
            self.config_manager.get_setting("ui.follow_mouse", True)
        )
        self.animation_cb.setChecked(
            self.config_manager.get_setting("ui.animation_enabled", True)
        )
        self.blur_background_cb.setChecked(
            self.config_manager.get_setting("ui.blur_background", True)
        )
    
    def save_settings(self):
        """保存設置"""
        # 應用程序設置
        self.config_manager.set_setting("app.auto_start", self.auto_start_cb.isChecked())
        self.config_manager.set_setting("app.minimize_to_tray", self.minimize_to_tray_cb.isChecked())
        self.config_manager.set_setting("app.close_to_tray", self.close_to_tray_cb.isChecked())
        
        # 界面設置
        theme_map = {0: "auto", 1: "light", 2: "dark"}
        self.config_manager.set_setting("ui.theme", theme_map[self.theme_combo.currentIndex()])
        self.config_manager.set_setting("ui.opacity", self.opacity_slider.value() / 100.0)
        self.config_manager.set_setting("ui.corner_radius", self.corner_radius_spin.value())
        self.config_manager.set_setting("ui.follow_mouse", self.follow_mouse_cb.isChecked())
        self.config_manager.set_setting("ui.animation_enabled", self.animation_cb.isChecked())
        self.config_manager.set_setting("ui.blur_background", self.blur_background_cb.isChecked())


class HotkeySettingsTab(QWidget):
    """熱鍵設置標籤頁"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 熱鍵設置組
        hotkey_group = QGroupBox("全局熱鍵")
        hotkey_layout = QFormLayout(hotkey_group)
        
        # 主面板熱鍵
        self.main_hotkey_edit = QLineEdit()
        self.main_hotkey_edit.setPlaceholderText("點擊設置熱鍵")
        hotkey_layout.addRow("顯示主面板:", self.main_hotkey_edit)
        
        # 待辦熱鍵
        self.todo_hotkey_edit = QLineEdit()
        self.todo_hotkey_edit.setPlaceholderText("點擊設置熱鍵")
        hotkey_layout.addRow("待辦事項:", self.todo_hotkey_edit)
        
        # 設置熱鍵
        self.settings_hotkey_edit = QLineEdit()
        self.settings_hotkey_edit.setPlaceholderText("點擊設置熱鍵")
        hotkey_layout.addRow("設置面板:", self.settings_hotkey_edit)
        
        layout.addWidget(hotkey_group)
        
        # 說明
        help_label = QLabel(
            "提示：\n"
            "• 支持的修飾鍵：Ctrl, Shift, Alt, Win\n"
            "• 示例：Ctrl+Space, Ctrl+Shift+T\n"
            "• 點擊輸入框後按下組合鍵即可設置"
        )
        help_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(help_label)
        
        layout.addStretch()
    
    def _load_settings(self):
        """加載設置"""
        self.main_hotkey_edit.setText(
            self.config_manager.get_setting("hotkeys.main", "ctrl+space")
        )
        self.todo_hotkey_edit.setText(
            self.config_manager.get_setting("hotkeys.todo", "ctrl+shift+t")
        )
        self.settings_hotkey_edit.setText(
            self.config_manager.get_setting("hotkeys.settings", "ctrl+shift+s")
        )
    
    def save_settings(self):
        """保存設置"""
        self.config_manager.set_setting("hotkeys.main", self.main_hotkey_edit.text())
        self.config_manager.set_setting("hotkeys.todo", self.todo_hotkey_edit.text())
        self.config_manager.set_setting("hotkeys.settings", self.settings_hotkey_edit.text())


class SearchSettingsTab(QWidget):
    """搜索設置標籤頁"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 搜索設置組
        search_group = QGroupBox("搜索設置")
        search_layout = QFormLayout(search_group)
        
        # 最大結果數
        self.max_results_spin = QSpinBox()
        self.max_results_spin.setRange(10, 200)
        search_layout.addRow("最大結果數:", self.max_results_spin)
        
        # 搜索延遲
        self.search_delay_spin = QSpinBox()
        self.search_delay_spin.setRange(100, 2000)
        self.search_delay_spin.setSuffix(" ms")
        search_layout.addRow("搜索延遲:", self.search_delay_spin)
        
        # 啟用Everything
        self.enable_everything_cb = QCheckBox("啟用Everything搜索")
        search_layout.addRow(self.enable_everything_cb)
        
        # 啟用內容搜索
        self.enable_content_search_cb = QCheckBox("啟用文件內容搜索")
        search_layout.addRow(self.enable_content_search_cb)
        
        # 搜索歷史
        self.search_history_cb = QCheckBox("保存搜索歷史")
        search_layout.addRow(self.search_history_cb)
        
        layout.addWidget(search_group)
        layout.addStretch()
    
    def _load_settings(self):
        """加載設置"""
        self.max_results_spin.setValue(
            self.config_manager.get_setting("search.max_results", 50)
        )
        self.search_delay_spin.setValue(
            self.config_manager.get_setting("search.search_delay", 300)
        )
        self.enable_everything_cb.setChecked(
            self.config_manager.get_setting("search.enable_everything", True)
        )
        self.enable_content_search_cb.setChecked(
            self.config_manager.get_setting("search.enable_content_search", True)
        )
        self.search_history_cb.setChecked(
            self.config_manager.get_setting("search.search_history", True)
        )
    
    def save_settings(self):
        """保存設置"""
        self.config_manager.set_setting("search.max_results", self.max_results_spin.value())
        self.config_manager.set_setting("search.search_delay", self.search_delay_spin.value())
        self.config_manager.set_setting("search.enable_everything", self.enable_everything_cb.isChecked())
        self.config_manager.set_setting("search.enable_content_search", self.enable_content_search_cb.isChecked())
        self.config_manager.set_setting("search.search_history", self.search_history_cb.isChecked())


class SettingsWindow(QDialog):
    """設置窗口"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 設置標籤頁
        self.tabs: Dict[str, QWidget] = {}
        
        # 初始化UI
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("設置")
        self.setFixedSize(500, 600)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 創建標籤頁控件
        self.tab_widget = QTabWidget()
        
        # 添加標籤頁
        self.tabs["general"] = GeneralSettingsTab(self.config_manager)
        self.tab_widget.addTab(self.tabs["general"], "常規")
        
        self.tabs["hotkey"] = HotkeySettingsTab(self.config_manager)
        self.tab_widget.addTab(self.tabs["hotkey"], "熱鍵")
        
        self.tabs["search"] = SearchSettingsTab(self.config_manager)
        self.tab_widget.addTab(self.tabs["search"], "搜索")
        
        layout.addWidget(self.tab_widget)
        
        # 按鈕布局
        button_layout = QHBoxLayout()
        
        # 重置按鈕
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self._reset_settings)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        # 取消按鈕
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        # 確定按鈕
        self.ok_btn = QPushButton("確定")
        self.ok_btn.clicked.connect(self._save_and_close)
        self.ok_btn.setDefault(True)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
        
        # 設置字體
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
    
    def _save_and_close(self):
        """保存設置並關閉"""
        try:
            # 保存所有標籤頁的設置
            for tab in self.tabs.values():
                if hasattr(tab, 'save_settings'):
                    tab.save_settings()
            
            # 發送設置改變信號
            self.settings_changed.emit()
            
            # 關閉對話框
            self.accept()
            
            self.logger.info("設置已保存")
            
        except Exception as e:
            self.logger.error(f"保存設置失敗: {e}", exc_info=True)
            QMessageBox.critical(self, "錯誤", f"保存設置失敗：{str(e)}")
    
    def _reset_settings(self):
        """重置設置"""
        reply = QMessageBox.question(
            self,
            "確認重置",
            "確定要重置所有設置為默認值嗎？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 重置配置
                self.config_manager.reset_all_settings()
                
                # 重新加載設置
                for tab in self.tabs.values():
                    if hasattr(tab, '_load_settings'):
                        tab._load_settings()
                
                QMessageBox.information(self, "成功", "設置已重置為默認值")
                
                self.logger.info("設置已重置")
                
            except Exception as e:
                self.logger.error(f"重置設置失敗: {e}", exc_info=True)
                QMessageBox.critical(self, "錯誤", f"重置設置失敗：{str(e)}")
