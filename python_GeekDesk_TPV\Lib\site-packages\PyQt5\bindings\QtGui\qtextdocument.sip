// qtextdocument.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace Qt
{
%TypeHeaderCode
#include <qtextdocument.h>
%End

    bool mightBeRichText(const QString &);
    QString convertFromPlainText(const QString &plain, Qt::WhiteSpaceMode mode = Qt::WhiteSpacePre);
};

class QTextDocument : public QObject
{
%TypeHeaderCode
#include <qtextdocument.h>
%End

public:
    explicit QTextDocument(QObject *parent /TransferThis/ = 0);
    QTextDocument(const QString &text, QObject *parent /TransferThis/ = 0);
    virtual ~QTextDocument();
    QTextDocument *clone(QObject *parent /TransferThis/ = 0) const /Factory/;
    bool isEmpty() const;
    virtual void clear();
    void setUndoRedoEnabled(bool enable);
    bool isUndoRedoEnabled() const;
    bool isUndoAvailable() const;
    bool isRedoAvailable() const;
    void setDocumentLayout(QAbstractTextDocumentLayout *layout /Transfer/);
    QAbstractTextDocumentLayout *documentLayout() const;

    enum MetaInformation
    {
        DocumentTitle,
        DocumentUrl,
    };

    void setMetaInformation(QTextDocument::MetaInformation info, const QString &);
    QString metaInformation(QTextDocument::MetaInformation info) const;
    QString toHtml(const QByteArray &encoding = QByteArray()) const;
    void setHtml(const QString &html);
    QString toPlainText() const;
    void setPlainText(const QString &text);

    enum FindFlag
    {
        FindBackward,
        FindCaseSensitively,
        FindWholeWords,
    };

    typedef QFlags<QTextDocument::FindFlag> FindFlags;
    QTextCursor find(const QString &subString, int position = 0, QFlags<QTextDocument::FindFlag> options = 0) const;
    QTextCursor find(const QRegExp &expr, int position = 0, QFlags<QTextDocument::FindFlag> options = 0) const;
%If (Qt_5_5_0 -)
    QTextCursor find(const QRegularExpression &expr, int position = 0, QFlags<QTextDocument::FindFlag> options = 0) const;
%End
    QTextCursor find(const QString &subString, const QTextCursor &cursor, QFlags<QTextDocument::FindFlag> options = 0) const;
    QTextCursor find(const QRegExp &expr, const QTextCursor &cursor, QFlags<QTextDocument::FindFlag> options = 0) const;
%If (Qt_5_5_0 -)
    QTextCursor find(const QRegularExpression &expr, const QTextCursor &cursor, QFlags<QTextDocument::FindFlag> options = 0) const;
%End
    QTextFrame *rootFrame() const;
    QTextObject *object(int objectIndex) const;
    QTextObject *objectForFormat(const QTextFormat &) const;
    QTextBlock findBlock(int pos) const;
    QTextBlock begin() const;
    QTextBlock end() const;
    void setPageSize(const QSizeF &size);
    QSizeF pageSize() const;
    void setDefaultFont(const QFont &font);
    QFont defaultFont() const;
    int pageCount() const;
    bool isModified() const;
%If (PyQt_Printer)
    void print(QPagedPaintDevice *printer) const /PyName=print_/;
%End
%If (Py_v3)
%If (PyQt_Printer)
    void print(QPagedPaintDevice *printer) const;
%End
%End

    enum ResourceType
    {
%If (Qt_5_14_0 -)
        UnknownResource,
%End
        HtmlResource,
        ImageResource,
        StyleSheetResource,
%If (Qt_5_14_0 -)
        MarkdownResource,
%End
        UserResource,
    };

    QVariant resource(int type, const QUrl &name) const;
    void addResource(int type, const QUrl &name, const QVariant &resource);
    QVector<QTextFormat> allFormats() const;
    void markContentsDirty(int from, int length);
    void setUseDesignMetrics(bool b);
    bool useDesignMetrics() const;

signals:
    void blockCountChanged(int newBlockCount);
    void contentsChange(int from, int charsRemoves, int charsAdded);
    void contentsChanged();
    void cursorPositionChanged(const QTextCursor &cursor);
    void modificationChanged(bool m);
    void redoAvailable(bool);
    void undoAvailable(bool);

public slots:
    void undo();
    void redo();
    void setModified(bool on = true);

protected:
    virtual QTextObject *createObject(const QTextFormat &f) /Factory/;
    virtual QVariant loadResource(int type, const QUrl &name);

public:
    void drawContents(QPainter *p, const QRectF &rect = QRectF());
    void setTextWidth(qreal width);
    qreal textWidth() const;
    qreal idealWidth() const;
    void adjustSize();
    QSizeF size() const;
    int blockCount() const;
    void setDefaultStyleSheet(const QString &sheet);
    QString defaultStyleSheet() const;
    void undo(QTextCursor *cursor);
    void redo(QTextCursor *cursor);
    int maximumBlockCount() const;
    void setMaximumBlockCount(int maximum);
    QTextOption defaultTextOption() const;
    void setDefaultTextOption(const QTextOption &option);
    int revision() const;
    QTextBlock findBlockByNumber(int blockNumber) const;
    QTextBlock findBlockByLineNumber(int blockNumber) const;
    QTextBlock firstBlock() const;
    QTextBlock lastBlock() const;
    qreal indentWidth() const;
    void setIndentWidth(qreal width);

signals:
    void undoCommandAdded();
    void documentLayoutChanged();

public:
    QChar characterAt(int pos) const;
    qreal documentMargin() const;
    void setDocumentMargin(qreal margin);
    int lineCount() const;
    int characterCount() const;
    int availableUndoSteps() const;
    int availableRedoSteps() const;

    enum Stacks
    {
        UndoStack,
        RedoStack,
        UndoAndRedoStacks,
    };

    void clearUndoRedoStacks(QTextDocument::Stacks stacks = QTextDocument::UndoAndRedoStacks);
    Qt::CursorMoveStyle defaultCursorMoveStyle() const;
    void setDefaultCursorMoveStyle(Qt::CursorMoveStyle style);
%If (Qt_5_3_0 -)
    QUrl baseUrl() const;
%End
%If (Qt_5_3_0 -)
    void setBaseUrl(const QUrl &url);
%End

signals:
%If (Qt_5_3_0 -)
    void baseUrlChanged(const QUrl &url);
%End

public:
%If (Qt_5_9_0 -)
    QString toRawText() const;
%End
%If (Qt_5_14_0 -)

    enum MarkdownFeature
    {
        MarkdownNoHTML,
        MarkdownDialectCommonMark,
        MarkdownDialectGitHub,
    };

%End
%If (Qt_5_14_0 -)
    typedef QFlags<QTextDocument::MarkdownFeature> MarkdownFeatures;
%End
%If (Qt_5_14_0 -)
    QString toMarkdown(QTextDocument::MarkdownFeatures features = QTextDocument::MarkdownDialectGitHub) const;
%End
%If (Qt_5_14_0 -)
    void setMarkdown(const QString &markdown, QTextDocument::MarkdownFeatures features = QTextDocument::MarkdownDialectGitHub);
%End
};

QFlags<QTextDocument::FindFlag> operator|(QTextDocument::FindFlag f1, QFlags<QTextDocument::FindFlag> f2);
