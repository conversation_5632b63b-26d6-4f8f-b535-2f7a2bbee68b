// qopengltexture.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)
%If (PyQt_OpenGL)

class QOpenGLTexture
{
%TypeHeaderCode
#include <qopengltexture.h>
%End

public:
    enum Target
    {
        Target1D,
        Target1DArray,
        Target2D,
        Target2DArray,
        Target3D,
        TargetCubeMap,
        TargetCubeMapArray,
        Target2DMultisample,
        Target2DMultisampleArray,
        TargetRectangle,
        TargetBuffer,
    };

    enum BindingTarget
    {
        BindingTarget1D,
        BindingTarget1DArray,
        BindingTarget2D,
        BindingTarget2DArray,
        BindingTarget3D,
        BindingTargetCubeMap,
        BindingTargetCubeMapArray,
        BindingTarget2DMultisample,
        BindingTarget2DMultisampleArray,
        BindingTargetRectangle,
        BindingTargetBuffer,
    };

    enum MipMapGeneration
    {
        GenerateMipMaps,
        DontGenerateMipMaps,
    };

    enum TextureUnitReset
    {
        ResetTextureUnit,
        DontResetTextureUnit,
    };

    explicit QOpenGLTexture(QOpenGLTexture::Target target);
    QOpenGLTexture(const QImage &image, QOpenGLTexture::MipMapGeneration genMipMaps = QOpenGLTexture::GenerateMipMaps);
    ~QOpenGLTexture();
    bool create();
    void destroy();
    bool isCreated() const;
    GLuint textureId() const;
    void bind();
    void bind(uint unit, QOpenGLTexture::TextureUnitReset reset = QOpenGLTexture::DontResetTextureUnit);
    void release();
    void release(uint unit, QOpenGLTexture::TextureUnitReset reset = QOpenGLTexture::DontResetTextureUnit);
    bool isBound() const;
    bool isBound(uint unit);
    static GLuint boundTextureId(QOpenGLTexture::BindingTarget target);
    static GLuint boundTextureId(uint unit, QOpenGLTexture::BindingTarget target);

    enum TextureFormat
    {
        NoFormat,
        R8_UNorm,
        RG8_UNorm,
        RGB8_UNorm,
        RGBA8_UNorm,
        R16_UNorm,
        RG16_UNorm,
        RGB16_UNorm,
        RGBA16_UNorm,
        R8_SNorm,
        RG8_SNorm,
        RGB8_SNorm,
        RGBA8_SNorm,
        R16_SNorm,
        RG16_SNorm,
        RGB16_SNorm,
        RGBA16_SNorm,
        R8U,
        RG8U,
        RGB8U,
        RGBA8U,
        R16U,
        RG16U,
        RGB16U,
        RGBA16U,
        R32U,
        RG32U,
        RGB32U,
        RGBA32U,
        R8I,
        RG8I,
        RGB8I,
        RGBA8I,
        R16I,
        RG16I,
        RGB16I,
        RGBA16I,
        R32I,
        RG32I,
        RGB32I,
        RGBA32I,
        R16F,
        RG16F,
        RGB16F,
        RGBA16F,
        R32F,
        RG32F,
        RGB32F,
        RGBA32F,
        RGB9E5,
        RG11B10F,
        RG3B2,
        R5G6B5,
        RGB5A1,
        RGBA4,
        RGB10A2,
        D16,
        D24,
        D24S8,
        D32,
        D32F,
        D32FS8X24,
        RGB_DXT1,
        RGBA_DXT1,
        RGBA_DXT3,
        RGBA_DXT5,
        R_ATI1N_UNorm,
        R_ATI1N_SNorm,
        RG_ATI2N_UNorm,
        RG_ATI2N_SNorm,
        RGB_BP_UNSIGNED_FLOAT,
        RGB_BP_SIGNED_FLOAT,
        RGB_BP_UNorm,
        SRGB8,
        SRGB8_Alpha8,
        SRGB_DXT1,
        SRGB_Alpha_DXT1,
        SRGB_Alpha_DXT3,
        SRGB_Alpha_DXT5,
        SRGB_BP_UNorm,
        DepthFormat,
        AlphaFormat,
        RGBFormat,
        RGBAFormat,
        LuminanceFormat,
        LuminanceAlphaFormat,
%If (Qt_5_4_0 -)
        S8,
%End
%If (Qt_5_5_0 -)
        R11_EAC_UNorm,
%End
%If (Qt_5_5_0 -)
        R11_EAC_SNorm,
%End
%If (Qt_5_5_0 -)
        RG11_EAC_UNorm,
%End
%If (Qt_5_5_0 -)
        RG11_EAC_SNorm,
%End
%If (Qt_5_5_0 -)
        RGB8_ETC2,
%End
%If (Qt_5_5_0 -)
        SRGB8_ETC2,
%End
%If (Qt_5_5_0 -)
        RGB8_PunchThrough_Alpha1_ETC2,
%End
%If (Qt_5_5_0 -)
        SRGB8_PunchThrough_Alpha1_ETC2,
%End
%If (Qt_5_5_0 -)
        RGBA8_ETC2_EAC,
%End
%If (Qt_5_5_0 -)
        SRGB8_Alpha8_ETC2_EAC,
%End
%If (Qt_5_6_0 -)
        RGB8_ETC1,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_4x4,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_5x4,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_5x5,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_6x5,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_6x6,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_8x5,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_8x6,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_8x8,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_10x5,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_10x6,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_10x8,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_10x10,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_12x10,
%End
%If (Qt_5_9_0 -)
        RGBA_ASTC_12x12,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_4x4,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_5x4,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_5x5,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_6x5,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_6x6,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_8x5,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_8x6,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_8x8,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_10x5,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_10x6,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_10x8,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_10x10,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_12x10,
%End
%If (Qt_5_9_0 -)
        SRGB8_Alpha8_ASTC_12x12,
%End
    };

    void setFormat(QOpenGLTexture::TextureFormat format);
    QOpenGLTexture::TextureFormat format() const;
    void setSize(int width, int height = 1, int depth = 1);
    int width() const;
    int height() const;
    int depth() const;
    void setMipLevels(int levels);
    int mipLevels() const;
    int maximumMipLevels() const;
    void setLayers(int layers);
    int layers() const;
    int faces() const;
    void allocateStorage();
%If (Qt_5_5_0 -)
    void allocateStorage(QOpenGLTexture::PixelFormat pixelFormat, QOpenGLTexture::PixelType pixelType);
%End
    bool isStorageAllocated() const;
    QOpenGLTexture *createTextureView(QOpenGLTexture::Target target, QOpenGLTexture::TextureFormat viewFormat, int minimumMipmapLevel, int maximumMipmapLevel, int minimumLayer, int maximumLayer) const /Factory/;
    bool isTextureView() const;

    enum CubeMapFace
    {
        CubeMapPositiveX,
        CubeMapNegativeX,
        CubeMapPositiveY,
        CubeMapNegativeY,
        CubeMapPositiveZ,
        CubeMapNegativeZ,
    };

    enum PixelFormat
    {
        NoSourceFormat,
        Red,
        RG,
        RGB,
        BGR,
        RGBA,
        BGRA,
        Red_Integer,
        RG_Integer,
        RGB_Integer,
        BGR_Integer,
        RGBA_Integer,
        BGRA_Integer,
        Depth,
        DepthStencil,
        Alpha,
        Luminance,
        LuminanceAlpha,
%If (Qt_5_4_0 -)
        Stencil,
%End
    };

    enum PixelType
    {
        NoPixelType,
        Int8,
        UInt8,
        Int16,
        UInt16,
        Int32,
        UInt32,
        Float16,
        Float16OES,
        Float32,
        UInt32_RGB9_E5,
        UInt32_RG11B10F,
        UInt8_RG3B2,
        UInt8_RG3B2_Rev,
        UInt16_RGB5A1,
        UInt16_RGB5A1_Rev,
        UInt16_R5G6B5,
        UInt16_R5G6B5_Rev,
        UInt16_RGBA4,
        UInt16_RGBA4_Rev,
        UInt32_RGB10A2,
        UInt32_RGB10A2_Rev,
%If (Qt_5_4_0 -)
        UInt32_RGBA8,
%End
%If (Qt_5_4_0 -)
        UInt32_RGBA8_Rev,
%End
%If (Qt_5_4_0 -)
        UInt32_D24S8,
%End
%If (Qt_5_4_0 -)
        Float32_D32_UInt32_S8_X24,
%End
    };

%If (Qt_5_3_0 -)
    void setData(int mipLevel, int layer, QOpenGLTexture::CubeMapFace cubeFace, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (- Qt_5_3_0)
    void setData(int mipLevel, int layer, QOpenGLTexture::CubeMapFace cubeFace, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_3_0 -)
    void setData(int mipLevel, int layer, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (- Qt_5_3_0)
    void setData(int mipLevel, int layer, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_3_0 -)
    void setData(int mipLevel, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (- Qt_5_3_0)
    void setData(int mipLevel, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_3_0 -)
    void setData(QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (- Qt_5_3_0)
    void setData(QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
    void setData(const QImage &image, QOpenGLTexture::MipMapGeneration genMipMaps = QOpenGLTexture::GenerateMipMaps);
%If (Qt_5_3_0 -)
    void setCompressedData(int mipLevel, int layer, QOpenGLTexture::CubeMapFace cubeFace, int dataSize, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (- Qt_5_3_0)
    void setCompressedData(int mipLevel, int layer, QOpenGLTexture::CubeMapFace cubeFace, int dataSize, void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_3_0 -)
    void setCompressedData(int mipLevel, int layer, int dataSize, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (- Qt_5_3_0)
    void setCompressedData(int mipLevel, int layer, int dataSize, void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_3_0 -)
    void setCompressedData(int mipLevel, int dataSize, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (- Qt_5_3_0)
    void setCompressedData(int mipLevel, int dataSize, void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_3_0 -)
    void setCompressedData(int dataSize, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (- Qt_5_3_0)
    void setCompressedData(int dataSize, void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End

    enum Feature
    {
        ImmutableStorage,
        ImmutableMultisampleStorage,
        TextureRectangle,
        TextureArrays,
        Texture3D,
        TextureMultisample,
        TextureBuffer,
        TextureCubeMapArrays,
        Swizzle,
        StencilTexturing,
        AnisotropicFiltering,
        NPOTTextures,
        NPOTTextureRepeat,
%If (Qt_5_3_0 -)
        Texture1D,
%End
%If (Qt_5_5_0 -)
        TextureComparisonOperators,
%End
%If (Qt_5_5_0 -)
        TextureMipMapLevel,
%End
    };

    typedef QFlags<QOpenGLTexture::Feature> Features;
    static bool hasFeature(QOpenGLTexture::Feature feature);
    void setMipBaseLevel(int baseLevel);
    int mipBaseLevel() const;
    void setMipMaxLevel(int maxLevel);
    int mipMaxLevel() const;
    void setMipLevelRange(int baseLevel, int maxLevel);
    QPair<int, int> mipLevelRange() const;
    void setAutoMipMapGenerationEnabled(bool enabled);
    bool isAutoMipMapGenerationEnabled() const;
    void generateMipMaps();
    void generateMipMaps(int baseLevel, bool resetBaseLevel = true);

    enum SwizzleComponent
    {
        SwizzleRed,
        SwizzleGreen,
        SwizzleBlue,
        SwizzleAlpha,
    };

    enum SwizzleValue
    {
        RedValue,
        GreenValue,
        BlueValue,
        AlphaValue,
        ZeroValue,
        OneValue,
    };

    void setSwizzleMask(QOpenGLTexture::SwizzleComponent component, QOpenGLTexture::SwizzleValue value);
    void setSwizzleMask(QOpenGLTexture::SwizzleValue r, QOpenGLTexture::SwizzleValue g, QOpenGLTexture::SwizzleValue b, QOpenGLTexture::SwizzleValue a);
    QOpenGLTexture::SwizzleValue swizzleMask(QOpenGLTexture::SwizzleComponent component) const;

    enum DepthStencilMode
    {
        DepthMode,
        StencilMode,
    };

    void setDepthStencilMode(QOpenGLTexture::DepthStencilMode mode);
    QOpenGLTexture::DepthStencilMode depthStencilMode() const;

    enum Filter
    {
        Nearest,
        Linear,
        NearestMipMapNearest,
        NearestMipMapLinear,
        LinearMipMapNearest,
        LinearMipMapLinear,
    };

    void setMinificationFilter(QOpenGLTexture::Filter filter);
    QOpenGLTexture::Filter minificationFilter() const;
    void setMagnificationFilter(QOpenGLTexture::Filter filter);
    QOpenGLTexture::Filter magnificationFilter() const;
    void setMinMagFilters(QOpenGLTexture::Filter minificationFilter, QOpenGLTexture::Filter magnificationFilter);
    QPair<QOpenGLTexture::Filter, QOpenGLTexture::Filter> minMagFilters() const;
    void setMaximumAnisotropy(float anisotropy);
    float maximumAnisotropy() const;

    enum WrapMode
    {
        Repeat,
        MirroredRepeat,
        ClampToEdge,
        ClampToBorder,
    };

    enum CoordinateDirection
    {
        DirectionS,
        DirectionT,
        DirectionR,
    };

    void setWrapMode(QOpenGLTexture::WrapMode mode);
    void setWrapMode(QOpenGLTexture::CoordinateDirection direction, QOpenGLTexture::WrapMode mode);
    QOpenGLTexture::WrapMode wrapMode(QOpenGLTexture::CoordinateDirection direction) const;
    void setBorderColor(QColor color);
    QColor borderColor() const;
    void setMinimumLevelOfDetail(float value);
    float minimumLevelOfDetail() const;
    void setMaximumLevelOfDetail(float value);
    float maximumLevelOfDetail() const;
    void setLevelOfDetailRange(float min, float max);
    QPair<float, float> levelOfDetailRange() const;
    void setLevelofDetailBias(float bias);
    float levelofDetailBias() const;
%If (Qt_5_4_0 -)
    QOpenGLTexture::Target target() const;
%End
%If (Qt_5_4_0 -)
    void setSamples(int samples);
%End
%If (Qt_5_4_0 -)
    int samples() const;
%End
%If (Qt_5_4_0 -)
    void setFixedSamplePositions(bool fixed);
%End
%If (Qt_5_4_0 -)
    bool isFixedSamplePositions() const;
%End
%If (Qt_5_5_0 -)

    enum ComparisonFunction
    {
        CompareLessEqual,
        CompareGreaterEqual,
        CompareLess,
        CompareGreater,
        CompareEqual,
        CommpareNotEqual,
        CompareAlways,
        CompareNever,
    };

%End
%If (Qt_5_5_0 -)
    void setComparisonFunction(QOpenGLTexture::ComparisonFunction function);
%End
%If (Qt_5_5_0 -)
    QOpenGLTexture::ComparisonFunction comparisonFunction() const;
%End
%If (Qt_5_5_0 -)

    enum ComparisonMode
    {
        CompareRefToTexture,
        CompareNone,
    };

%End
%If (Qt_5_5_0 -)
    void setComparisonMode(QOpenGLTexture::ComparisonMode mode);
%End
%If (Qt_5_5_0 -)
    QOpenGLTexture::ComparisonMode comparisonMode() const;
%End
%If (Qt_5_9_0 -)
    void setData(int mipLevel, int layer, int layerCount, QOpenGLTexture::CubeMapFace cubeFace, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_9_0 -)
    void setCompressedData(int mipLevel, int layer, int layerCount, QOpenGLTexture::CubeMapFace cubeFace, int dataSize, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_14_0 -)
    void setData(int xOffset, int yOffset, int zOffset, int width, int height, int depth, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_14_0 -)
    void setData(int xOffset, int yOffset, int zOffset, int width, int height, int depth, int mipLevel, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_14_0 -)
    void setData(int xOffset, int yOffset, int zOffset, int width, int height, int depth, int mipLevel, int layer, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_14_0 -)
    void setData(int xOffset, int yOffset, int zOffset, int width, int height, int depth, int mipLevel, int layer, QOpenGLTexture::CubeMapFace cubeFace, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End
%If (Qt_5_14_0 -)
    void setData(int xOffset, int yOffset, int zOffset, int width, int height, int depth, int mipLevel, int layer, QOpenGLTexture::CubeMapFace cubeFace, int layerCount, QOpenGLTexture::PixelFormat sourceFormat, QOpenGLTexture::PixelType sourceType, const void *data, const QOpenGLPixelTransferOptions * const options = 0);
%End

private:
    QOpenGLTexture(const QOpenGLTexture &);
};

%End
%End
%If (Qt_5_2_0 -)
%If (PyQt_OpenGL)
QFlags<QOpenGLTexture::Feature> operator|(QOpenGLTexture::Feature f1, QFlags<QOpenGLTexture::Feature> f2);
%End
%End
