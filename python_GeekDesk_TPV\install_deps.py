#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依賴包安裝腳本
"""

import subprocess
import sys

def install_package(package_name, use_mirror=False):
    """安裝單個包"""
    print(f"正在安裝 {package_name}...")
    
    if use_mirror:
        cmd = [sys.executable, "-m", "pip", "install", "-i", "https://pypi.tuna.tsinghua.edu.cn/simple", package_name]
    else:
        cmd = [sys.executable, "-m", "pip", "install", package_name]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {package_name} 安裝成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安裝失敗: {e}")
        return False

def main():
    """主函數"""
    print("python_GeekDesk_TPV 依賴包安裝程序")
    print("=" * 50)
    
    # 需要安裝的包
    packages = ["PyQt5", "psutil", "keyboard"]
    
    # 首先嘗試升級 pip
    print("升級 pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip 升級成功")
    except:
        print("⚠️ pip 升級失敗，繼續安裝...")
    
    print()
    
    # 安裝每個包
    failed_packages = []
    
    for i, package in enumerate(packages, 1):
        print(f"步驟 {i}/{len(packages)}: 安裝 {package}")
        
        # 首先嘗試默認源
        if not install_package(package):
            print(f"默認源失敗，嘗試使用清華鏡像源...")
            if not install_package(package, use_mirror=True):
                failed_packages.append(package)
        
        print()
    
    # 總結
    print("=" * 50)
    if not failed_packages:
        print("🎉 所有依賴包安裝成功！")
        print("現在可以運行 'python main.py' 啟動程序")
    else:
        print("❌ 以下包安裝失敗:")
        for pkg in failed_packages:
            print(f"  - {pkg}")
        print("\n請嘗試手動安裝:")
        print(f"pip install {' '.join(failed_packages)}")
    
    input("\n按 Enter 鍵退出...")

if __name__ == "__main__":
    main()
