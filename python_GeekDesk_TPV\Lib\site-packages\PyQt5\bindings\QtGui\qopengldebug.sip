// qopengldebug.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)

class QOpenGLDebugMessage
{
%TypeHeaderCode
#include <qopengldebug.h>
%End

public:
    enum Source
    {
        InvalidSource,
        APISource,
        WindowSystemSource,
        ShaderCompilerSource,
        ThirdPartySource,
        ApplicationSource,
        OtherSource,
        AnySource,
    };

    typedef QFlags<QOpenGLDebugMessage::Source> Sources;

    enum Type
    {
        InvalidType,
        ErrorType,
        DeprecatedBehaviorType,
        UndefinedBehaviorType,
        PortabilityType,
        PerformanceType,
        OtherType,
        MarkerType,
        GroupPushType,
        GroupPopType,
        AnyType,
    };

    typedef QFlags<QOpenGLDebugMessage::Type> Types;

    enum Severity
    {
        InvalidSeverity,
        HighSeverity,
        MediumSeverity,
        LowSeverity,
        NotificationSeverity,
        AnySeverity,
    };

    typedef QFlags<QOpenGLDebugMessage::Severity> Severities;
    QOpenGLDebugMessage();
    QOpenGLDebugMessage(const QOpenGLDebugMessage &debugMessage);
    ~QOpenGLDebugMessage();
    void swap(QOpenGLDebugMessage &debugMessage /Constrained/);
    QOpenGLDebugMessage::Source source() const;
    QOpenGLDebugMessage::Type type() const;
    QOpenGLDebugMessage::Severity severity() const;
    GLuint id() const;
    QString message() const;
    static QOpenGLDebugMessage createApplicationMessage(const QString &text, GLuint id = 0, QOpenGLDebugMessage::Severity severity = QOpenGLDebugMessage::NotificationSeverity, QOpenGLDebugMessage::Type type = QOpenGLDebugMessage::OtherType);
    static QOpenGLDebugMessage createThirdPartyMessage(const QString &text, GLuint id = 0, QOpenGLDebugMessage::Severity severity = QOpenGLDebugMessage::NotificationSeverity, QOpenGLDebugMessage::Type type = QOpenGLDebugMessage::OtherType);
    bool operator==(const QOpenGLDebugMessage &debugMessage) const;
    bool operator!=(const QOpenGLDebugMessage &debugMessage) const;
};

%End
%End
%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)
QFlags<QOpenGLDebugMessage::Source> operator|(QOpenGLDebugMessage::Source f1, QFlags<QOpenGLDebugMessage::Source> f2);
%End
%End
%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)
QFlags<QOpenGLDebugMessage::Type> operator|(QOpenGLDebugMessage::Type f1, QFlags<QOpenGLDebugMessage::Type> f2);
%End
%End
%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)
QFlags<QOpenGLDebugMessage::Severity> operator|(QOpenGLDebugMessage::Severity f1, QFlags<QOpenGLDebugMessage::Severity> f2);
%End
%End
%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)

class QOpenGLDebugLogger : public QObject
{
%TypeHeaderCode
#include <qopengldebug.h>
%End

public:
    enum LoggingMode
    {
        AsynchronousLogging,
        SynchronousLogging,
    };

    explicit QOpenGLDebugLogger(QObject *parent /TransferThis/ = 0);
    virtual ~QOpenGLDebugLogger();
    bool initialize();
    bool isLogging() const;
    QOpenGLDebugLogger::LoggingMode loggingMode() const;
    qint64 maximumMessageLength() const;
    void pushGroup(const QString &name, GLuint id = 0, QOpenGLDebugMessage::Source source = QOpenGLDebugMessage::ApplicationSource);
    void popGroup();
    void enableMessages(QOpenGLDebugMessage::Sources sources = QOpenGLDebugMessage::AnySource, QOpenGLDebugMessage::Types types = QOpenGLDebugMessage::AnyType, QOpenGLDebugMessage::Severities severities = QOpenGLDebugMessage::Severity::AnySeverity);
    void enableMessages(const QVector<unsigned int> &ids, QOpenGLDebugMessage::Sources sources = QOpenGLDebugMessage::AnySource, QOpenGLDebugMessage::Types types = QOpenGLDebugMessage::AnyType);
    void disableMessages(QOpenGLDebugMessage::Sources sources = QOpenGLDebugMessage::AnySource, QOpenGLDebugMessage::Types types = QOpenGLDebugMessage::AnyType, QOpenGLDebugMessage::Severities severities = QOpenGLDebugMessage::Severity::AnySeverity);
    void disableMessages(const QVector<unsigned int> &ids, QOpenGLDebugMessage::Sources sources = QOpenGLDebugMessage::AnySource, QOpenGLDebugMessage::Types types = QOpenGLDebugMessage::AnyType);
    QList<QOpenGLDebugMessage> loggedMessages() const;

public slots:
    void logMessage(const QOpenGLDebugMessage &debugMessage);
    void startLogging(QOpenGLDebugLogger::LoggingMode loggingMode = QOpenGLDebugLogger::AsynchronousLogging);
    void stopLogging();

signals:
    void messageLogged(const QOpenGLDebugMessage &debugMessage);

private:
    QOpenGLDebugLogger(const QOpenGLDebugLogger &);
};

%End
%End
