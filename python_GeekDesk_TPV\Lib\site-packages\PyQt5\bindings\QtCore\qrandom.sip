// qrandom.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_10_0 -)

class QRandomGenerator
{
%TypeHeaderCode
#include <qrandom.h>
%End

public:
    QRandomGenerator(quint32 seed = 1);
    QRandomGenerator(const QRandomGenerator &other);
    quint32 generate();
    quint64 generate64();
    double generateDouble();
    double bounded(double highest /Constrained/);
    quint32 bounded(quint32 highest);
    int bounded(int lowest, int highest);
    typedef quint32 result_type;
    QRandomGenerator::result_type operator()();
    void seed(quint32 seed = 1);
    void discard(unsigned long long z);
    static QRandomGenerator::result_type min();
    static QRandomGenerator::result_type max();
    static QRandomGenerator *system();
    static QRandomGenerator *global() /PyName=global_/;
    static QRandomGenerator securelySeeded();
};

%End
%If (Qt_5_10_0 -)
bool operator==(const QRandomGenerator &rng1, const QRandomGenerator &rng2);
%End
%If (Qt_5_10_0 -)
bool operator!=(const QRandomGenerator &rng1, const QRandomGenerator &rng2);
%End
