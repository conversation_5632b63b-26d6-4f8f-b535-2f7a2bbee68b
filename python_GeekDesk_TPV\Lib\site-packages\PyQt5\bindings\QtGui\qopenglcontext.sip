// qopenglcontext.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_OpenGL)

class QOpenGLContextGroup : public QObject
{
%TypeHeaderCode
#include <qopenglcontext.h>
%End

public:
    virtual ~QOpenGLContextGroup();
    QList<QOpenGLContext *> shares() const;
    static QOpenGLContextGroup *currentContextGroup();

private:
    QOpenGLContextGroup();
};

%End
%If (PyQt_OpenGL)

class QOpenGLContext : public QObject
{
%TypeHeaderCode
#include <qopenglcontext.h>
%End

public:
    explicit QOpenGLContext(QObject *parent /TransferThis/ = 0);
    virtual ~QOpenGLContext();
    void setFormat(const QSurfaceFormat &format);
    void setShareContext(QOpenGLContext *shareContext);
    void setScreen(QScreen *screen);
    bool create();
    bool isValid() const;
    QSurfaceFormat format() const;
    QOpenGLContext *shareContext() const;
    QOpenGLContextGroup *shareGroup() const;
    QScreen *screen() const;
    GLuint defaultFramebufferObject() const;
    bool makeCurrent(QSurface *surface);
    void doneCurrent();
    void swapBuffers(QSurface *surface);
    QFunctionPointer getProcAddress(const QByteArray &procName) const;
    QSurface *surface() const;
    static QOpenGLContext *currentContext();
    static bool areSharing(QOpenGLContext *first, QOpenGLContext *second);
    QSet<QByteArray> extensions() const;
    bool hasExtension(const QByteArray &extension) const;

signals:
    void aboutToBeDestroyed();

public:
%If (Qt_5_1_0 -)
    SIP_PYOBJECT versionFunctions(const QOpenGLVersionProfile *versionProfile = 0) const;
%MethodCode
        sipRes = qpyopengl_version_functions(sipCpp, sipSelf, a0);
%End

%End
%If (Qt_5_3_0 -)
    static void *openGLModuleHandle();
%End
%If (Qt_5_3_0 -)

    enum OpenGLModuleType
    {
        LibGL,
        LibGLES,
    };

%End
%If (Qt_5_3_0 -)
    static QOpenGLContext::OpenGLModuleType openGLModuleType();
%End
%If (Qt_5_3_0 -)
    bool isOpenGLES() const;
%End
%If (Qt_5_4_0 -)
    void setNativeHandle(const QVariant &handle);
%End
%If (Qt_5_4_0 -)
    QVariant nativeHandle() const;
%End
%If (Qt_5_5_0 -)
    static bool supportsThreadedOpenGL();
%End
%If (Qt_5_5_0 -)
    static QOpenGLContext *globalShareContext();
%End
};

%End
%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)

class QOpenGLVersionProfile
{
%TypeHeaderCode
#include <qopenglcontext.h>
%End

public:
    QOpenGLVersionProfile();
    explicit QOpenGLVersionProfile(const QSurfaceFormat &format);
    QOpenGLVersionProfile(const QOpenGLVersionProfile &other);
    ~QOpenGLVersionProfile();
    QPair<int, int> version() const;
    void setVersion(int majorVersion, int minorVersion);
    QSurfaceFormat::OpenGLContextProfile profile() const;
    void setProfile(QSurfaceFormat::OpenGLContextProfile profile);
    bool hasProfiles() const;
    bool isLegacyVersion() const;
    bool isValid() const;
};

%End
%End
%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)
bool operator==(const QOpenGLVersionProfile &lhs, const QOpenGLVersionProfile &rhs);
%End
%End
%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)
bool operator!=(const QOpenGLVersionProfile &lhs, const QOpenGLVersionProfile &rhs);
%End
%End
