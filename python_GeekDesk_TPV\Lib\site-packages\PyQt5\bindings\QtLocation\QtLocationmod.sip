// QtLocationmod.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt5.QtLocation, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtPositioning/QtPositioningmod.sip

%Copying
Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt5.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype sip.simplewrapper

%Include qgeocodereply.sip
%Include qgeocodingmanager.sip
%Include qgeocodingmanagerengine.sip
%Include qgeomaneuver.sip
%Include qgeoroute.sip
%Include qgeoroutereply.sip
%Include qgeorouterequest.sip
%Include qgeoroutesegment.sip
%Include qgeoroutingmanager.sip
%Include qgeoroutingmanagerengine.sip
%Include qgeoserviceprovider.sip
%Include qlocation.sip
%Include qplace.sip
%Include qplaceattribute.sip
%Include qplacecategory.sip
%Include qplacecontactdetail.sip
%Include qplacecontent.sip
%Include qplacecontentreply.sip
%Include qplacecontentrequest.sip
%Include qplacedetailsreply.sip
%Include qplaceeditorial.sip
%Include qplaceicon.sip
%Include qplaceidreply.sip
%Include qplaceimage.sip
%Include qplacemanager.sip
%Include qplacemanagerengine.sip
%Include qplacematchreply.sip
%Include qplacematchrequest.sip
%Include qplaceproposedsearchresult.sip
%Include qplaceratings.sip
%Include qplacereply.sip
%Include qplaceresult.sip
%Include qplacereview.sip
%Include qplacesearchreply.sip
%Include qplacesearchrequest.sip
%Include qplacesearchresult.sip
%Include qplacesearchsuggestionreply.sip
%Include qplacesupplier.sip
%Include qplaceuser.sip
