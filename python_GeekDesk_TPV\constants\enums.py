#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
枚舉類型定義
"""

from enum import Enum, IntEnum


class AppHideType(IntEnum):
    """應用隱藏類型"""
    NONE = 0
    MINIMIZE = 1
    HIDE = 2
    CLOSE = 3


class BGStyle(IntEnum):
    """背景樣式"""
    SOLID_COLOR = 0
    GRADIENT = 1
    IMAGE = 2
    BLUR = 3


class HotKeyType(IntEnum):
    """熱鍵類型"""
    MAIN_PANEL = 0
    TODO = 1
    SEARCH = 2
    SETTINGS = 3


class IconType(IntEnum):
    """圖標類型"""
    SYSTEM = 0
    FILE = 1
    CUSTOM = 2
    ONLINE = 3


class MenuType(IntEnum):
    """菜單類型"""
    APPLICATION = 0
    FILE = 1
    FOLDER = 2
    URL = 3
    COMMAND = 4


class SearchType(IntEnum):
    """搜索類型"""
    FILE_NAME = 0
    FILE_CONTENT = 1
    EVERYTHING = 2
    REGISTRY = 3


class SortType(IntEnum):
    """排序類型"""
    NAME = 0
    SIZE = 1
    DATE_MODIFIED = 2
    DATE_CREATED = 3
    TYPE = 4


class TodoTaskExecType(IntEnum):
    """待辦任務執行類型"""
    ONCE = 0
    DAILY = 1
    WEEKLY = 2
    MONTHLY = 3
    YEARLY = 4


class UpdateType(IntEnum):
    """更新類型"""
    NONE = 0
    MINOR = 1
    MAJOR = 2
    CRITICAL = 3


class WindowState(IntEnum):
    """窗口狀態"""
    HIDDEN = 0
    NORMAL = 1
    MINIMIZED = 2
    MAXIMIZED = 3
    FULLSCREEN = 4


class ThemeType(IntEnum):
    """主題類型"""
    LIGHT = 0
    DARK = 1
    AUTO = 2


class AnimationType(IntEnum):
    """動畫類型"""
    NONE = 0
    FADE = 1
    SLIDE = 2
    SCALE = 3
    BOUNCE = 4


class NotificationType(IntEnum):
    """通知類型"""
    INFO = 0
    WARNING = 1
    ERROR = 2
    SUCCESS = 3
