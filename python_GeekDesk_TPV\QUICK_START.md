# python_GeekDesk_TPV 快速開始指南

## 🚀 三種啟動方式

### 方式一：一鍵安裝並啟動（推薦）
```bash
# 雙擊運行
install.bat
```
然後運行 `start.bat` 啟動程序

### 方式二：快速啟動（自動安裝依賴）
```bash
# 雙擊運行
quick_start.bat
```

### 方式三：手動安裝
```bash
# 1. 安裝依賴
pip install PyQt5 psutil pynput keyboard watchdog Pillow requests PyYAML colorlog python-dateutil

# 2. 運行程序
python main.py
```

## 🔧 如果遇到問題

### 問題1：找不到 Python
**錯誤**: `'python' is not recognized as an internal or external command`

**解決方案**:
1. 下載並安裝 Python: https://www.python.org/downloads/
2. 安裝時**務必勾選** "Add Python to PATH"
3. 重新打開命令提示符

### 問題2：找不到 PyQt5
**錯誤**: `ModuleNotFoundError: No module named 'PyQt5'`

**解決方案**:
```bash
# 使用國內鏡像源安裝
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple PyQt5
```

### 問題3：權限問題
**錯誤**: `Permission denied` 或 `Access is denied`

**解決方案**:
1. 以管理員身份運行命令提示符
2. 或者使用用戶安裝: `pip install --user PyQt5`

### 問題4：網絡連接問題
**錯誤**: `Could not fetch URL` 或超時

**解決方案**:
```bash
# 使用不同的鏡像源
pip install -i https://mirrors.aliyun.com/pypi/simple PyQt5
# 或
pip install -i https://pypi.douban.com/simple PyQt5
```

## 📋 最小依賴安裝

如果只想快速體驗，可以只安裝核心依賴：
```bash
pip install PyQt5 psutil
```

## 🎯 使用方法

1. **啟動程序**: 運行後程序會最小化到系統托盤
2. **呼出面板**: 使用熱鍵 `Ctrl+Space`
3. **搜索文件**: 在搜索框中輸入關鍵詞
4. **打開設置**: 右鍵托盤圖標選擇"設置"
5. **待辦事項**: 使用熱鍵 `Ctrl+Shift+T`

## 🔥 核心功能

- **文件搜索**: 快速搜索電腦中的文件
- **應用啟動**: 自定義快速啟動菜單
- **全局熱鍵**: 隨時呼出工具面板
- **待辦提醒**: 管理日常任務和提醒
- **界面美化**: 透明效果和圓角設計

## 📞 獲取幫助

如果仍然遇到問題：
1. 查看 `logs/` 目錄下的日誌文件
2. 檢查 `README.md` 中的詳細說明
3. 提交 Issue 到 GitHub 項目頁面

---

**提示**: 首次運行可能需要幾分鐘來安裝依賴包，請耐心等待。
