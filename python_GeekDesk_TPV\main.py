#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
python_GeekDesk_TPV - 桌面效率工具主程序
作者: Your Name
版本: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """檢查必要的依賴包"""
    missing_packages = []

    try:
        import PyQt5
    except ImportError:
        missing_packages.append("PyQt5")

    try:
        import psutil
    except ImportError:
        missing_packages.append("psutil")

    try:
        import keyboard
    except ImportError:
        missing_packages.append("keyboard")

    if missing_packages:
        print("錯誤: 缺少必要的依賴包:")
        for pkg in missing_packages:
            print(f"  - {pkg}")
        print("\n請運行以下命令安裝依賴:")
        print("pip install " + " ".join(missing_packages))
        print("\n或者運行 install.bat 自動安裝所有依賴")
        input("\n按 Enter 鍵退出...")
        return False

    return True

# 檢查依賴
if not check_dependencies():
    sys.exit(1)

from PyQt5.QtWidgets import QApplication, QSystemTrayIcon, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

from core.app import GeekDeskApp
from utils.logger import setup_logger
from utils.config_manager import ConfigManager
from constants.constants import APP_NAME, APP_VERSION


def check_single_instance():
    """檢查是否已有實例運行"""
    import tempfile
    import fcntl
    
    lock_file = os.path.join(tempfile.gettempdir(), f"{APP_NAME}.lock")
    
    try:
        lock_fd = os.open(lock_file, os.O_CREAT | os.O_TRUNC | os.O_RDWR)
        fcntl.lockf(lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
        return True, lock_fd
    except (IOError, OSError):
        return False, None


def setup_application():
    """設置應用程序"""
    # 設置高DPI支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 創建應用程序實例
    app = QApplication(sys.argv)
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    app.setQuitOnLastWindowClosed(False)
    
    # 設置應用程序圖標
    icon_path = project_root / "resources" / "icons" / "logo.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    return app


def check_system_requirements():
    """檢查系統要求"""
    # 檢查系統托盤支持
    if not QSystemTrayIcon.isSystemTrayAvailable():
        QMessageBox.critical(
            None, 
            "系統托盤不可用",
            "此應用程序需要系統托盤支持才能運行。"
        )
        return False
    
    # 檢查Python版本
    if sys.version_info < (3, 8):
        QMessageBox.critical(
            None,
            "Python版本過低", 
            "此應用程序需要Python 3.8或更高版本。"
        )
        return False
    
    return True


def main():
    """主函數"""
    try:
        # 檢查單實例
        is_single, lock_fd = check_single_instance()
        if not is_single:
            QMessageBox.information(
                None,
                "程序已運行",
                f"{APP_NAME} 已經在運行中。"
            )
            sys.exit(1)
        
        # 設置日誌
        logger = setup_logger()
        logger.info(f"啟動 {APP_NAME} v{APP_VERSION}")
        
        # 設置應用程序
        app = setup_application()
        
        # 檢查系統要求
        if not check_system_requirements():
            sys.exit(1)
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 創建主應用程序
        geek_desk = GeekDeskApp(config_manager)
        
        # 顯示啟動消息
        if geek_desk.tray_icon:
            geek_desk.tray_icon.showMessage(
                APP_NAME,
                "程序已啟動，點擊托盤圖標或使用熱鍵呼出面板",
                QSystemTrayIcon.Information,
                3000
            )
        
        logger.info("應用程序初始化完成")
        
        # 運行應用程序
        exit_code = app.exec_()
        
        # 清理資源
        if lock_fd:
            os.close(lock_fd)
        
        logger.info("應用程序退出")
        return exit_code
        
    except Exception as e:
        # 記錄錯誤
        if 'logger' in locals():
            logger.error(f"應用程序啟動失敗: {e}", exc_info=True)
        else:
            print(f"應用程序啟動失敗: {e}")
        
        # 顯示錯誤對話框
        try:
            app = QApplication.instance()
            if not app:
                app = QApplication(sys.argv)
            
            QMessageBox.critical(
                None,
                "啟動失敗",
                f"應用程序啟動失敗:\n{str(e)}\n\n請檢查日誌文件獲取詳細信息。"
            )
        except:
            pass
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
