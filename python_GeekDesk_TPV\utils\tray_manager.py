#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系統托盤管理器
"""

import logging
from typing import Optional, List, Dict, Callable
from pathlib import Path

from PyQt5.QtWidgets import QSystemTrayIcon, QMenu, QAction, QApplication
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QPixmap

from utils.config_manager import ConfigManager
from constants.constants import ICONS_DIR, APP_NAME
from constants.enums import NotificationType


class TrayManager(QObject):
    """系統托盤管理器"""
    
    # 信號定義
    tray_activated = pyqtSignal(int)  # 托盤圖標激活
    menu_action_triggered = pyqtSignal(str)  # 菜單動作觸發
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 托盤組件
        self.tray_icon: Optional[QSystemTrayIcon] = None
        self.tray_menu: Optional[QMenu] = None
        
        # 菜單動作
        self._menu_actions: Dict[str, QAction] = {}
        self._action_callbacks: Dict[str, Callable] = {}
        
        # 通知定時器
        self._notification_timer = QTimer()
        self._notification_timer.setSingleShot(True)
        
        # 初始化托盤
        self._init_tray()
    
    def _init_tray(self):
        """初始化系統托盤"""
        try:
            # 檢查系統托盤可用性
            if not QSystemTrayIcon.isSystemTrayAvailable():
                self.logger.error("系統托盤不可用")
                return
            
            # 創建托盤圖標
            self.tray_icon = QSystemTrayIcon()
            
            # 設置圖標
            self._set_tray_icon()
            
            # 設置工具提示
            self.tray_icon.setToolTip(APP_NAME)
            
            # 創建托盤菜單
            self._create_tray_menu()
            
            # 連接信號
            self.tray_icon.activated.connect(self._on_tray_activated)
            
            # 顯示托盤圖標
            self.tray_icon.show()
            
            self.logger.info("系統托盤初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化系統托盤失敗: {e}", exc_info=True)
    
    def _set_tray_icon(self, icon_path: Optional[str] = None):
        """設置托盤圖標"""
        try:
            if icon_path is None:
                icon_path = ICONS_DIR / "logo.ico"
            else:
                icon_path = Path(icon_path)
            
            if icon_path.exists():
                icon = QIcon(str(icon_path))
            else:
                # 使用默認圖標
                icon = QApplication.style().standardIcon(
                    QApplication.style().SP_ComputerIcon
                )
            
            if self.tray_icon:
                self.tray_icon.setIcon(icon)
                
        except Exception as e:
            self.logger.error(f"設置托盤圖標失敗: {e}")
    
    def _create_tray_menu(self):
        """創建托盤菜單"""
        try:
            self.tray_menu = QMenu()
            
            # 添加默認菜單項
            self._add_default_menu_items()
            
            # 設置托盤菜單
            if self.tray_icon:
                self.tray_icon.setContextMenu(self.tray_menu)
                
        except Exception as e:
            self.logger.error(f"創建托盤菜單失敗: {e}")
    
    def _add_default_menu_items(self):
        """添加默認菜單項"""
        # 顯示主面板
        self.add_menu_action(
            "show_main",
            "顯示主面板",
            icon_name="show",
            callback=lambda: self.menu_action_triggered.emit("show_main")
        )
        
        # 待辦事項
        self.add_menu_action(
            "show_todo",
            "待辦事項",
            icon_name="todo",
            callback=lambda: self.menu_action_triggered.emit("show_todo")
        )
        
        # 分隔符
        self.add_menu_separator()
        
        # 設置
        self.add_menu_action(
            "show_settings",
            "設置",
            icon_name="settings",
            callback=lambda: self.menu_action_triggered.emit("show_settings")
        )
        
        # 關於
        self.add_menu_action(
            "show_about",
            "關於",
            icon_name="about",
            callback=lambda: self.menu_action_triggered.emit("show_about")
        )
        
        # 分隔符
        self.add_menu_separator()
        
        # 退出
        self.add_menu_action(
            "quit",
            "退出",
            icon_name="quit",
            callback=lambda: self.menu_action_triggered.emit("quit")
        )
    
    def add_menu_action(self, action_id: str, text: str, 
                       icon_name: Optional[str] = None,
                       callback: Optional[Callable] = None,
                       enabled: bool = True) -> QAction:
        """
        添加菜單動作
        
        Args:
            action_id: 動作ID
            text: 顯示文本
            icon_name: 圖標名稱
            callback: 回調函數
            enabled: 是否啟用
        
        Returns:
            創建的QAction對象
        """
        try:
            action = QAction(text)
            action.setEnabled(enabled)
            
            # 設置圖標
            if icon_name:
                icon_path = ICONS_DIR / f"{icon_name}.png"
                if icon_path.exists():
                    action.setIcon(QIcon(str(icon_path)))
            
            # 連接回調
            if callback:
                action.triggered.connect(callback)
                self._action_callbacks[action_id] = callback
            
            # 添加到菜單
            if self.tray_menu:
                self.tray_menu.addAction(action)
            
            # 保存引用
            self._menu_actions[action_id] = action
            
            return action
            
        except Exception as e:
            self.logger.error(f"添加菜單動作失敗: {e}")
            return QAction()
    
    def add_menu_separator(self):
        """添加菜單分隔符"""
        if self.tray_menu:
            self.tray_menu.addSeparator()
    
    def remove_menu_action(self, action_id: str):
        """移除菜單動作"""
        try:
            if action_id in self._menu_actions:
                action = self._menu_actions[action_id]
                
                # 從菜單中移除
                if self.tray_menu:
                    self.tray_menu.removeAction(action)
                
                # 清理引用
                del self._menu_actions[action_id]
                if action_id in self._action_callbacks:
                    del self._action_callbacks[action_id]
                
        except Exception as e:
            self.logger.error(f"移除菜單動作失敗: {e}")
    
    def update_menu_action(self, action_id: str, text: Optional[str] = None,
                          enabled: Optional[bool] = None,
                          visible: Optional[bool] = None):
        """
        更新菜單動作
        
        Args:
            action_id: 動作ID
            text: 新文本
            enabled: 是否啟用
            visible: 是否可見
        """
        try:
            if action_id in self._menu_actions:
                action = self._menu_actions[action_id]
                
                if text is not None:
                    action.setText(text)
                
                if enabled is not None:
                    action.setEnabled(enabled)
                
                if visible is not None:
                    action.setVisible(visible)
                
        except Exception as e:
            self.logger.error(f"更新菜單動作失敗: {e}")
    
    def _on_tray_activated(self, reason: int):
        """托盤圖標激活事件處理"""
        self.tray_activated.emit(reason)
        
        # 根據激活原因執行相應動作
        if reason == QSystemTrayIcon.DoubleClick:
            self.menu_action_triggered.emit("show_main")
        elif reason == QSystemTrayIcon.MiddleClick:
            self.menu_action_triggered.emit("show_main")
    
    def show_message(self, title: str, message: str, 
                    notification_type: NotificationType = NotificationType.INFO,
                    duration: int = 3000):
        """
        顯示托盤通知消息
        
        Args:
            title: 標題
            message: 消息內容
            notification_type: 通知類型
            duration: 顯示時長（毫秒）
        """
        try:
            if not self.tray_icon or not self.tray_icon.isVisible():
                return
            
            # 映射通知類型
            icon_map = {
                NotificationType.INFO: QSystemTrayIcon.Information,
                NotificationType.WARNING: QSystemTrayIcon.Warning,
                NotificationType.ERROR: QSystemTrayIcon.Critical,
                NotificationType.SUCCESS: QSystemTrayIcon.Information
            }
            
            icon = icon_map.get(notification_type, QSystemTrayIcon.Information)
            
            # 顯示消息
            self.tray_icon.showMessage(title, message, icon, duration)
            
            self.logger.debug(f"顯示托盤消息: {title} - {message}")
            
        except Exception as e:
            self.logger.error(f"顯示托盤消息失敗: {e}")
    
    def set_tooltip(self, tooltip: str):
        """設置托盤圖標工具提示"""
        try:
            if self.tray_icon:
                self.tray_icon.setToolTip(tooltip)
        except Exception as e:
            self.logger.error(f"設置工具提示失敗: {e}")
    
    def is_visible(self) -> bool:
        """檢查托盤圖標是否可見"""
        return self.tray_icon is not None and self.tray_icon.isVisible()
    
    def show(self):
        """顯示托盤圖標"""
        try:
            if self.tray_icon:
                self.tray_icon.show()
        except Exception as e:
            self.logger.error(f"顯示托盤圖標失敗: {e}")
    
    def hide(self):
        """隱藏托盤圖標"""
        try:
            if self.tray_icon:
                self.tray_icon.hide()
        except Exception as e:
            self.logger.error(f"隱藏托盤圖標失敗: {e}")
    
    def cleanup(self):
        """清理資源"""
        try:
            # 隱藏托盤圖標
            self.hide()
            
            # 清理菜單
            if self.tray_menu:
                self.tray_menu.clear()
                self.tray_menu = None
            
            # 清理托盤圖標
            self.tray_icon = None
            
            # 清理引用
            self._menu_actions.clear()
            self._action_callbacks.clear()
            
            self.logger.info("托盤管理器清理完成")
            
        except Exception as e:
            self.logger.error(f"清理托盤管理器失敗: {e}")
