@echo off
echo 正在安裝 python_GeekDesk_TPV 依賴包...
echo.

echo 步驟 1/3: 安裝 PyQt5...
pip install PyQt5
if errorlevel 1 (
    echo PyQt5 安裝失敗，嘗試使用鏡像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple PyQt5
)

echo.
echo 步驟 2/3: 安裝 psutil...
pip install psutil
if errorlevel 1 (
    echo psutil 安裝失敗，嘗試使用鏡像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple psutil
)

echo.
echo 步驟 3/3: 安裝 keyboard...
pip install keyboard
if errorlevel 1 (
    echo keyboard 安裝失敗，嘗試使用鏡像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple keyboard
)

echo.
echo 安裝完成！現在可以運行 python main.py 啟動程序
pause
