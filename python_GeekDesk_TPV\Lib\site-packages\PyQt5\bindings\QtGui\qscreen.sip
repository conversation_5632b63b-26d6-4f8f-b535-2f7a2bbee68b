// qscreen.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QScreen : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qscreen.h>
%End

public:
%If (Qt_5_4_0 -)
    virtual ~QScreen();
%End
    QString name() const;
    int depth() const;
    QSize size() const;
    QRect geometry() const;
    QSizeF physicalSize() const;
    qreal physicalDotsPerInchX() const;
    qreal physicalDotsPerInchY() const;
    qreal physicalDotsPerInch() const;
    qreal logicalDotsPerInchX() const;
    qreal logicalDotsPerInchY() const;
    qreal logicalDotsPerInch() const;
    QSize availableSize() const;
    QRect availableGeometry() const;
    QList<QScreen *> virtualSiblings() const;
    QSize virtualSize() const;
    QRect virtualGeometry() const;
    QSize availableVirtualSize() const;
    QRect availableVirtualGeometry() const;
%If (Qt_5_2_0 -)
    Qt::ScreenOrientation nativeOrientation() const;
%End
    Qt::ScreenOrientation primaryOrientation() const;
    Qt::ScreenOrientation orientation() const;
    Qt::ScreenOrientations orientationUpdateMask() const;
    void setOrientationUpdateMask(Qt::ScreenOrientations mask);
    int angleBetween(Qt::ScreenOrientation a, Qt::ScreenOrientation b) const;
    QTransform transformBetween(Qt::ScreenOrientation a, Qt::ScreenOrientation b, const QRect &target) const;
    QRect mapBetween(Qt::ScreenOrientation a, Qt::ScreenOrientation b, const QRect &rect) const;
    bool isPortrait(Qt::ScreenOrientation orientation) const;
    bool isLandscape(Qt::ScreenOrientation orientation) const;
    QPixmap grabWindow(WId window, int x = 0, int y = 0, int width = -1, int height = -1);
    qreal refreshRate() const;
    qreal devicePixelRatio() const;

signals:
    void geometryChanged(const QRect &geometry);
    void physicalDotsPerInchChanged(qreal dpi);
    void logicalDotsPerInchChanged(qreal dpi);
    void primaryOrientationChanged(Qt::ScreenOrientation orientation);
    void orientationChanged(Qt::ScreenOrientation orientation);
    void refreshRateChanged(qreal refreshRate);
    void physicalSizeChanged(const QSizeF &size);
    void virtualGeometryChanged(const QRect &rect);
%If (Qt_5_4_0 -)
    void availableGeometryChanged(const QRect &geometry);
%End

public:
%If (Qt_5_9_0 -)
    QString manufacturer() const;
%End
%If (Qt_5_9_0 -)
    QString model() const;
%End
%If (Qt_5_9_0 -)
    QString serialNumber() const;
%End
%If (Qt_5_15_0 -)
    QScreen *virtualSiblingAt(QPoint point);
%End

private:
    QScreen(const QScreen &);
};
