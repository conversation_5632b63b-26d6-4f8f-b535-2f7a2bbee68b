// qregexp.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRegExp
{
%TypeHeaderCode
#include <qregexp.h>
%End

public:
    enum PatternSyntax
    {
        RegExp,
        RegExp2,
        Wildcard,
        FixedString,
        WildcardUnix,
        W3CXmlSchema11,
    };

    enum CaretMode
    {
        CaretAtZero,
        CaretAtOffset,
        CaretWontMatch,
    };

    QRegExp();
    QRegExp(const QString &pattern, Qt::CaseSensitivity cs = Qt::CaseSensitive, QRegExp::PatternSyntax syntax = QRegExp::RegExp);
    QRegExp(const QRegExp &rx);
    ~QRegExp();
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *uni = qpycore_PyObject_FromQString(sipCpp->pattern());
        
        if (uni)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QRegExp(%R", uni);
        
            if (sipCpp->caseSensitivity() != Qt::CaseSensitive ||
                sipCpp->patternSyntax() != QRegExp::RegExp)
            {
                qpycore_Unicode_ConcatAndDel(&sipRes,
                        PyUnicode_FromFormat(", PyQt5.QtCore.Qt.CaseSensitivity(%i)",
                                (int)sipCpp->caseSensitivity()));
        
                if (sipCpp->patternSyntax() != QRegExp::RegExp)
                    qpycore_Unicode_ConcatAndDel(&sipRes,
                            PyUnicode_FromFormat(
                                    ", PyQt5.QtCore.QRegExp.PatternSyntax(%i)",
                                    (int)sipCpp->patternSyntax()));
            }
        
            qpycore_Unicode_ConcatAndDel(&sipRes, PyUnicode_FromString(")"));
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QRegExp(");
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(uni));
        
            if (sipCpp->caseSensitivity() != Qt::CaseSensitive ||
                sipCpp->patternSyntax() != QRegExp::RegExp)
            {
                PyString_ConcatAndDel(&sipRes,
                        PyString_FromFormat(", PyQt5.QtCore.Qt.CaseSensitivity(%i)",
                                (int)sipCpp->caseSensitivity()));
        
                if (sipCpp->patternSyntax() != QRegExp::RegExp)
                    PyString_ConcatAndDel(&sipRes,
                            PyString_FromFormat(
                                    ", PyQt5.QtCore.QRegExp.PatternSyntax(%i)",
                                    (int)sipCpp->patternSyntax()));
            }
        
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        
            Py_DECREF(uni);
        }
        else
        {
            sipRes = 0;
        }
%End

    bool operator==(const QRegExp &rx) const;
    bool operator!=(const QRegExp &rx) const;
    bool isEmpty() const;
    bool isValid() const;
    QString pattern() const;
    void setPattern(const QString &pattern);
    Qt::CaseSensitivity caseSensitivity() const;
    void setCaseSensitivity(Qt::CaseSensitivity cs);
    QRegExp::PatternSyntax patternSyntax() const;
    void setPatternSyntax(QRegExp::PatternSyntax syntax);
    bool isMinimal() const;
    void setMinimal(bool minimal);
    bool exactMatch(const QString &str) const;
    int indexIn(const QString &str, int offset = 0, QRegExp::CaretMode caretMode = QRegExp::CaretAtZero) const;
    int lastIndexIn(const QString &str, int offset = -1, QRegExp::CaretMode caretMode = QRegExp::CaretAtZero) const;
    int matchedLength() const;
    QStringList capturedTexts();
    QString cap(int nth = 0);
    int pos(int nth = 0);
    QString errorString();
    static QString escape(const QString &str);
    int captureCount() const;
    void swap(QRegExp &other /Constrained/);
%If (Qt_5_6_0 -)
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
};

QDataStream &operator<<(QDataStream &out, const QRegExp &regExp /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QRegExp &regExp /Constrained/) /ReleaseGIL/;
