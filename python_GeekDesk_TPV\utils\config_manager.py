#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional, Union
from threading import Lock

from constants.constants import (
    CONFIG_FILE, USER_CONFIG_FILE, DEFAULT_HOTKEY, DEFAULT_TODO_HOTKEY,
    DEFAULT_OPACITY, DEFAULT_CORNER_RADIUS, DEFAULT_WINDOW_WIDTH, DEFAULT_WINDOW_HEIGHT
)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._config_lock = Lock()
        self._config_cache = {}
        self._user_config_cache = {}
        
        # 加載配置
        self._load_configs()
    
    def _load_configs(self):
        """加載配置文件"""
        try:
            # 加載默認配置
            self._load_default_config()
            
            # 加載用戶配置
            self._load_user_config()
            
            self.logger.info("配置文件加載完成")
            
        except Exception as e:
            self.logger.error(f"加載配置文件失敗: {e}", exc_info=True)
            self._create_default_configs()
    
    def _load_default_config(self):
        """加載默認配置"""
        if CONFIG_FILE.exists():
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    self._config_cache = json.load(f)
            except Exception as e:
                self.logger.error(f"加載默認配置失敗: {e}")
                self._config_cache = {}
        else:
            self._create_default_config()
    
    def _load_user_config(self):
        """加載用戶配置"""
        if USER_CONFIG_FILE.exists():
            try:
                with open(USER_CONFIG_FILE, 'r', encoding='utf-8') as f:
                    self._user_config_cache = json.load(f)
            except Exception as e:
                self.logger.error(f"加載用戶配置失敗: {e}")
                self._user_config_cache = {}
        else:
            self._user_config_cache = {}
    
    def _create_default_config(self):
        """創建默認配置"""
        self._config_cache = {
            "app": {
                "name": "python_GeekDesk_TPV",
                "version": "1.0.0",
                "auto_start": False,
                "minimize_to_tray": True,
                "close_to_tray": True
            },
            "hotkeys": {
                "main": DEFAULT_HOTKEY,
                "todo": DEFAULT_TODO_HOTKEY,
                "settings": "ctrl+shift+s"
            },
            "ui": {
                "theme": "auto",
                "opacity": DEFAULT_OPACITY,
                "corner_radius": DEFAULT_CORNER_RADIUS,
                "window_width": DEFAULT_WINDOW_WIDTH,
                "window_height": DEFAULT_WINDOW_HEIGHT,
                "follow_mouse": True,
                "animation_enabled": True,
                "blur_background": True
            },
            "search": {
                "max_results": 50,
                "search_delay": 300,
                "enable_everything": True,
                "enable_content_search": True,
                "search_history": True
            },
            "todo": {
                "default_reminder": True,
                "reminder_sound": True,
                "reminder_popup": True,
                "auto_save": True
            },
            "menu": {
                "show_icons": True,
                "icon_size": 32,
                "grid_columns": 6,
                "show_labels": True
            }
        }
        
        self._save_config()
    
    def _create_default_configs(self):
        """創建默認配置文件"""
        self._create_default_config()
        self._user_config_cache = {}
        self._save_user_config()
    
    def _save_config(self):
        """保存默認配置"""
        try:
            CONFIG_FILE.parent.mkdir(parents=True, exist_ok=True)
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self._config_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存默認配置失敗: {e}")
    
    def _save_user_config(self):
        """保存用戶配置"""
        try:
            USER_CONFIG_FILE.parent.mkdir(parents=True, exist_ok=True)
            with open(USER_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self._user_config_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存用戶配置失敗: {e}")
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """
        獲取設置值
        
        Args:
            key: 設置鍵，支持點分隔的嵌套鍵
            default: 默認值
        
        Returns:
            設置值
        """
        with self._config_lock:
            # 首先從用戶配置中查找
            value = self._get_nested_value(self._user_config_cache, key)
            if value is not None:
                return value
            
            # 然後從默認配置中查找
            value = self._get_nested_value(self._config_cache, key)
            if value is not None:
                return value
            
            return default
    
    def set_setting(self, key: str, value: Any, save: bool = True):
        """
        設置配置值
        
        Args:
            key: 設置鍵，支持點分隔的嵌套鍵
            value: 設置值
            save: 是否立即保存
        """
        with self._config_lock:
            self._set_nested_value(self._user_config_cache, key, value)
            
            if save:
                self._save_user_config()
    
    def _get_nested_value(self, config: Dict, key: str) -> Any:
        """獲取嵌套字典中的值"""
        keys = key.split('.')
        current = config
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return None
        
        return current
    
    def _set_nested_value(self, config: Dict, key: str, value: Any):
        """設置嵌套字典中的值"""
        keys = key.split('.')
        current = config
        
        # 創建嵌套結構
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 設置值
        current[keys[-1]] = value
    
    def get_all_settings(self) -> Dict[str, Any]:
        """獲取所有設置"""
        with self._config_lock:
            # 合併默認配置和用戶配置
            merged = self._config_cache.copy()
            self._deep_merge(merged, self._user_config_cache)
            return merged
    
    def _deep_merge(self, base: Dict, update: Dict):
        """深度合併字典"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def reset_setting(self, key: str, save: bool = True):
        """
        重置設置為默認值
        
        Args:
            key: 設置鍵
            save: 是否立即保存
        """
        with self._config_lock:
            keys = key.split('.')
            current = self._user_config_cache
            
            # 找到父級字典
            for k in keys[:-1]:
                if k in current and isinstance(current[k], dict):
                    current = current[k]
                else:
                    return  # 鍵不存在
            
            # 刪除鍵
            if keys[-1] in current:
                del current[keys[-1]]
                
                if save:
                    self._save_user_config()
    
    def reset_all_settings(self, save: bool = True):
        """
        重置所有設置為默認值
        
        Args:
            save: 是否立即保存
        """
        with self._config_lock:
            self._user_config_cache = {}
            
            if save:
                self._save_user_config()
    
    def export_settings(self, file_path: Union[str, Path]) -> bool:
        """
        導出設置到文件
        
        Args:
            file_path: 導出文件路徑
        
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            settings = self.get_all_settings()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"設置已導出到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"導出設置失敗: {e}")
            return False
    
    def import_settings(self, file_path: Union[str, Path]) -> bool:
        """
        從文件導入設置
        
        Args:
            file_path: 導入文件路徑
        
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                self.logger.error(f"導入文件不存在: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            with self._config_lock:
                self._user_config_cache = settings
                self._save_user_config()
            
            self.logger.info(f"設置已從文件導入: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"導入設置失敗: {e}")
            return False
    
    def backup_settings(self, backup_dir: Optional[Path] = None) -> Optional[Path]:
        """
        備份設置
        
        Args:
            backup_dir: 備份目錄
        
        Returns:
            備份文件路徑
        """
        try:
            from datetime import datetime
            from constants.constants import BACKUP_DIR
            
            if backup_dir is None:
                backup_dir = BACKUP_DIR
            
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"settings_backup_{timestamp}.json"
            
            if self.export_settings(backup_file):
                self.logger.info(f"設置已備份到: {backup_file}")
                return backup_file
            
        except Exception as e:
            self.logger.error(f"備份設置失敗: {e}")
        
        return None
