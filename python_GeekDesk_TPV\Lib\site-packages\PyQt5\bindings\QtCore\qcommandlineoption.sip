// qcommandlineoption.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QCommandLineOption
{
%TypeHeaderCode
#include <qcommandlineoption.h>
%End

public:
%If (Qt_5_4_0 -)
    explicit QCommandLineOption(const QString &name);
%End
%If (Qt_5_4_0 -)
    explicit QCommandLineOption(const QStringList &names);
%End
%If (Qt_5_4_0 -)
    QCommandLineOption(const QString &name, const QString &description, const QString &valueName = QString(), const QString &defaultValue = QString());
%End
%If (- Qt_5_4_0)
    QCommandLineOption(const QString &name, const QString &description = QString(), const QString &valueName = QString(), const QString &defaultValue = QString());
%End
%If (Qt_5_4_0 -)
    QCommandLineOption(const QStringList &names, const QString &description, const QString &valueName = QString(), const QString &defaultValue = QString());
%End
%If (- Qt_5_4_0)
    QCommandLineOption(const QStringList &names, const QString &description = QString(), const QString &valueName = QString(), const QString &defaultValue = QString());
%End
    QCommandLineOption(const QCommandLineOption &other);
    ~QCommandLineOption();
    void swap(QCommandLineOption &other /Constrained/);
    QStringList names() const;
    void setValueName(const QString &name);
    QString valueName() const;
    void setDescription(const QString &description);
    QString description() const;
    void setDefaultValue(const QString &defaultValue);
    void setDefaultValues(const QStringList &defaultValues);
    QStringList defaultValues() const;
%If (Qt_5_6_0 -)
    void setHidden(bool hidden);
%End
%If (Qt_5_6_0 -)
    bool isHidden() const;
%End
%If (Qt_5_8_0 -)

    enum Flag
    {
        HiddenFromHelp,
        ShortOptionStyle,
    };

%End
%If (Qt_5_8_0 -)
    typedef QFlags<QCommandLineOption::Flag> Flags;
%End
%If (Qt_5_8_0 -)
    QCommandLineOption::Flags flags() const;
%End
%If (Qt_5_8_0 -)
    void setFlags(QCommandLineOption::Flags aflags);
%End
};

%End
%If (Qt_5_8_0 -)
QFlags<QCommandLineOption::Flag> operator|(QCommandLineOption::Flag f1, QFlags<QCommandLineOption::Flag> f2);
%End
