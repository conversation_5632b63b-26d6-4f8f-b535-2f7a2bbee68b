// qlibraryinfo.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLibraryInfo
{
%TypeHeaderCode
#include <qlibraryinfo.h>
%End

public:
    static QString licensee();
    static QString licensedProducts();

    enum LibraryLocation
    {
        PrefixPath,
        DocumentationPath,
        HeadersPath,
        LibrariesPath,
        BinariesPath,
        PluginsPath,
        DataPath,
        TranslationsPath,
        SettingsPath,
        ExamplesPath,
        ImportsPath,
        TestsPath,
        LibraryExecutablesPath,
        Qml2ImportsPath,
        ArchDataPath,
    };

    static QString location(QLibraryInfo::LibraryLocation) /ReleaseGIL/;
    static QDate buildDate();
    static bool isDebugBuild();
%If (Qt_5_8_0 -)
    static QVersionNumber version();
%End

private:
    QLibraryInfo();
};
