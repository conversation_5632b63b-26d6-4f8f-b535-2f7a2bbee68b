// qdesktopservices.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesktopServices
{
%TypeHeaderCode
#include <qdesktopservices.h>
%End

public:
    static bool openUrl(const QUrl &url) /ReleaseGIL/;
    static void setUrlHandler(const QString &scheme, QObject *receiver, const char *method);
    static void setUrlHandler(const QString &scheme, SIP_PYCALLABLE method /TypeHint="Callable[[QUrl], None]"/);
%MethodCode
        // Allow a callable that must be a slot of a QObject, although we never tell
        // the user if it isn't.
        sipMethodDef pm;
        
        if (sipGetMethod(a1, &pm))
        {
            int iserr = 0;
            QObject *receiver = reinterpret_cast<QObject *>(sipForceConvertToType(
                    pm.pm_self, sipType_QObject, NULL, SIP_NOT_NONE, NULL, &iserr));
        
            if (!iserr)
            {
                PyObject *f_name_obj = PyObject_GetAttrString(pm.pm_function, "__name__");
        
                if (f_name_obj)
                {
                    // We only want a borrowed reference.
                    Py_DECREF(f_name_obj);
        
                    const char *f_name = sipString_AsASCIIString(&f_name_obj);
        
                    if (f_name)
                    {
                        QDesktopServices::setUrlHandler(*a0, receiver, f_name);
        
                        Py_DECREF(f_name_obj);
                    }
                }
            }
        }
%End

    static void unsetUrlHandler(const QString &scheme);
};
