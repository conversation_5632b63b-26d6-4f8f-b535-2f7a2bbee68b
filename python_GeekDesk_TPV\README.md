# python_GeekDesk_TPV

一個基於 Python 的桌面效率工具，提供快速啟動、文件搜索、全局熱鍵等功能。

## ✨ 功能特色

- 🚀 **快速啟動**: 自定義菜單和圖標，快速啟動常用程序
- 🔍 **文件搜索**: 強大的文件和內容搜索功能
- ⌨️ **全局熱鍵**: 自定義快捷鍵，隨時呼出面板
- 🎨 **界面美化**: 支持自定義背景、透明度和圓角
- 📝 **待辦提醒**: 定時提醒功能，永不遺忘重要事項
- 🖱️ **鼠標跟隨**: 面板自動跟隨鼠標位置顯示
- 🔧 **高度自定義**: 豐富的配置選項，打造專屬桌面

## 🔥 核心功能

### 集成Everything 快速搜索全盤文件
- 支持文件名搜索
- 支持文件內容搜索
- 實時搜索結果

### 全局熱鍵 一鍵呼出 鼠標跟隨
- 自定義熱鍵 設置並使用自己習慣的快捷鍵
- 一鍵呼出 使用鼠標中鍵呼出
- 鼠標跟隨 自動追隨鼠標位置

### 自定義壁紙
- 隨意選擇自己喜歡的壁紙
- 支持多種圖片格式

### 毛玻璃等界面效果
- 背景圖片毛玻璃效果
- 界面透明度調節
- 界面圓角設計

### 自定義菜單圖標
- 80多個系統圖標可供選擇
- 另支持在線導入阿里巴巴icon圖標

### 定時提醒 永不忘記
- 快捷鍵快速新建待辦事項
- 定時提醒功能
- 任務管理

## 🛠️ 環境建置

### 系統要求
- Windows 10/11
- Python 3.8+

### 安裝步驟

1. **克隆專案**
```bash
git clone https://github.com/your-username/python_GeekDesk_TPV.git
cd python_GeekDesk_TPV
```

2. **創建虛擬環境**
```bash
python -m venv venv
venv\Scripts\activate
```

3. **安裝依賴**
```bash
pip install -r requirements.txt
```

4. **運行程序**
```bash
python main.py
```

## 🚀 啟動方式

### 開發模式
```bash
python main.py
```

### 打包執行
```bash
# 安裝打包工具
pip install pyinstaller

# 打包為單個執行文件
pyinstaller --onefile --windowed --icon=resources/icons/logo.ico main.py
```

## 📖 使用範例

### 基本使用
1. 啟動程序後，程序會最小化到系統托盤
2. 使用默認熱鍵 `Ctrl+Space` 呼出主面板
3. 在搜索框中輸入關鍵詞進行文件搜索
4. 點擊應用圖標快速啟動程序

### 自定義配置
1. 右鍵托盤圖標選擇"設置"
2. 在設置面板中可以：
   - 修改全局熱鍵
   - 添加/編輯快速啟動項目
   - 設置界面主題和透明度
   - 配置搜索選項

### 添加快速啟動項目
```python
# 在配置文件中添加新的啟動項目
{
    "name": "記事本",
    "path": "notepad.exe",
    "icon": "notepad",
    "hotkey": "Ctrl+N"
}
```

## 🤝 貢獻方法

我們歡迎任何形式的貢獻！

### 如何貢獻
1. Fork 這個專案
2. 創建你的功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交你的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟一個 Pull Request

### 開發指南
- 遵循 PEP 8 代碼風格
- 為新功能添加測試
- 更新相關文檔
- 確保所有測試通過

## 🐛 疑難排解

### 常見問題

**Q: 程序無法啟動**
A: 請檢查：
- Python 版本是否為 3.8+
- 是否正確安裝了所有依賴
- 是否有足夠的系統權限

**Q: 熱鍵不生效**
A: 請檢查：
- 熱鍵是否與其他程序衝突
- 是否以管理員權限運行
- 重新設置熱鍵組合

**Q: 搜索功能無法使用**
A: 請檢查：
- Everything 是否已安裝並運行
- 搜索索引是否建立完成
- 文件權限設置

**Q: 界面顯示異常**
A: 請嘗試：
- 重置界面設置
- 更新顯卡驅動
- 檢查系統DPI設置

### 日誌文件
程序運行日誌保存在 `logs/` 目錄下，遇到問題時可以查看詳細錯誤信息。

### 獲取幫助
- 提交 Issue: [GitHub Issues](https://github.com/your-username/python_GeekDesk_TPV/issues)
- 討論區: [GitHub Discussions](https://github.com/your-username/python_GeekDesk_TPV/discussions)
- 郵件聯繫: <EMAIL>

## 📄 許可證

本專案採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 🙏 致謝

- 感謝 [Everything](https://www.voidtools.com/) 提供強大的文件搜索功能
- 感謝所有貢獻者的努力
- 靈感來源於原版 C# GeekDesk 項目

## 📊 專案狀態

![GitHub stars](https://img.shields.io/github/stars/your-username/python_GeekDesk_TPV)
![GitHub forks](https://img.shields.io/github/forks/your-username/python_GeekDesk_TPV)
![GitHub issues](https://img.shields.io/github/issues/your-username/python_GeekDesk_TPV)
![GitHub license](https://img.shields.io/github/license/your-username/python_GeekDesk_TPV)

---

⭐ 如果這個專案對你有幫助，請給我們一個 Star！
