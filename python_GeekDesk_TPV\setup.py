#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
python_GeekDesk_TPV 安裝腳本
"""

from setuptools import setup, find_packages
from pathlib import Path

# 讀取 README 文件
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# 讀取 requirements 文件
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]

setup(
    name="python_GeekDesk_TPV",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="一個基於 Python 的桌面效率工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/python_GeekDesk_TPV",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Desktop Environment",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-qt>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "build": [
            "pyinstaller>=5.0.0",
            "auto-py-to-exe>=2.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "geekdesk=main:main",
        ],
        "gui_scripts": [
            "geekdesk-gui=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "python_GeekDesk_TPV": [
            "resources/icons/*.ico",
            "resources/icons/*.png",
            "resources/images/*.png",
            "resources/images/*.jpg",
            "config/*.json",
            "docs/*.md",
        ],
    },
    data_files=[
        ("", ["README.md", "LICENSE"]),
    ],
    zip_safe=False,
    keywords="desktop utility launcher search todo hotkey",
    project_urls={
        "Bug Reports": "https://github.com/your-username/python_GeekDesk_TPV/issues",
        "Source": "https://github.com/your-username/python_GeekDesk_TPV",
        "Documentation": "https://github.com/your-username/python_GeekDesk_TPV/blob/main/docs/DEVELOPMENT.md",
    },
)
