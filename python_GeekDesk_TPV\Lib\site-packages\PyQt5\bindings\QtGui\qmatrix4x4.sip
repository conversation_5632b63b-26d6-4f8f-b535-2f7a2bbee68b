// qmatrix4x4.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qmatrix4x4.h>
%End

class QMatrix4x4
{
%TypeHeaderCode
#include <qmatrix4x4.h>
%End

%PickleCode
    PYQT_FLOAT data[16];
    
    // We want the data in row-major order.
    sipCpp->copyDataTo(data);
    
    sipRes = Py_BuildValue((char *)"dddddddddddddddd",
            (double)data[0], (double)data[1], (double)data[2], (double)data[3],
            (double)data[4], (double)data[5], (double)data[6], (double)data[7],
            (double)data[8], (double)data[9], (double)data[10], (double)data[11],
            (double)data[12], (double)data[13], (double)data[14], (double)data[15]);
%End

public:
    QMatrix4x4();
    explicit QMatrix4x4(SIP_PYOBJECT values /TypeHint="Sequence[float]"/) [(const float *values)];
%MethodCode
        float values[16];
        
        if ((sipError = qtgui_matrixDataFromSequence(a0, 16, values)) == sipErrorNone)
            sipCpp = new QMatrix4x4(values);
%End

    QMatrix4x4(float m11, float m12, float m13, float m14, float m21, float m22, float m23, float m24, float m31, float m32, float m33, float m34, float m41, float m42, float m43, float m44);
    QMatrix4x4(const QTransform &transform);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[16];
        PYQT_FLOAT data[16];
        
        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);
        
        for (i = 0; i < 16; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);
        
            if (!m[i])
                bad = true;
        }
        
        if (!bad)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtGui.QMatrix4x4("
                    "%R, %R, %R, %R, "
                    "%R, %R, %R, %R, "
                    "%R, %R, %R, %R, "
                    "%R, %R, %R, %R)",
                    m[0], m[1], m[2], m[3],
                    m[4], m[5], m[6], m[7],
                    m[8], m[9], m[10], m[11],
                    m[12], m[13], m[14], m[15]);
        #else
            sipRes = PyString_FromString("PyQt5.QtGui.QMatrix4x4(");
        
            for (i = 0; i < 16; ++i)
            {
                if (i != 0)
                    PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
        
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(m[i]));
            }
        
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        }
        
        for (i = 0; i < 16; ++i)
            Py_XDECREF(m[i]);
%End

    double determinant() const;
    QMatrix4x4 inverted(bool *invertible = 0) const;
    QMatrix4x4 transposed() const;
    QMatrix3x3 normalMatrix() const;
    void scale(const QVector3D &vector);
    void scale(float x, float y);
    void scale(float x, float y, float z);
    void scale(float factor);
    void translate(const QVector3D &vector);
    void translate(float x, float y);
    void translate(float x, float y, float z);
    void rotate(float angle, const QVector3D &vector);
    void rotate(float angle, float x, float y, float z = 0.F);
    void rotate(const QQuaternion &quaternion);
    void ortho(const QRect &rect);
    void ortho(const QRectF &rect);
    void ortho(float left, float right, float bottom, float top, float nearPlane, float farPlane);
    void frustum(float left, float right, float bottom, float top, float nearPlane, float farPlane);
    void perspective(float angle, float aspect, float nearPlane, float farPlane);
    void lookAt(const QVector3D &eye, const QVector3D &center, const QVector3D &up);
    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        float values[16];
        
        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(16, values, &sipRes);
%End

    QTransform toTransform() const;
    QTransform toTransform(float distanceToPlane) const;
    QRect mapRect(const QRect &rect) const;
    QRectF mapRect(const QRectF &rect) const;
    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(16, sipCpp->constData(), &sipRes);
%End

    void optimize();
    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;
        
        if ((sipError = qtgui_matrixParseIndex(a0, 4, 4, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));
        
            if (!sipRes)
                sipError = sipErrorFail;
        }
%End

    void __setitem__(SIP_PYOBJECT, qreal);
%MethodCode
        int row, column;
        
        if ((sipError = qtgui_matrixParseIndex(a0, 4, 4, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    QVector4D column(int index) const;
    void setColumn(int index, const QVector4D &value);
    QVector4D row(int index) const;
    void setRow(int index, const QVector4D &value);
    bool isIdentity() const;
    void setToIdentity();
    void fill(float value);
    QMatrix4x4 &operator+=(const QMatrix4x4 &other);
    QMatrix4x4 &operator-=(const QMatrix4x4 &other);
    QMatrix4x4 &operator*=(const QMatrix4x4 &other) /__imatmul__/;
    QMatrix4x4 &operator*=(float factor);
    QMatrix4x4 &operator/=(float divisor);
    bool operator==(const QMatrix4x4 &other) const;
    bool operator!=(const QMatrix4x4 &other) const;
    QPoint map(const QPoint &point) const;
    QPointF map(const QPointF &point) const;
    QVector3D map(const QVector3D &point) const;
    QVector3D mapVector(const QVector3D &vector) const;
    QVector4D map(const QVector4D &point) const;
%If (Qt_5_4_0 -)
    void viewport(float left, float bottom, float width, float height, float nearPlane = 0.F, float farPlane = 1.F);
%End
%If (Qt_5_4_0 -)
    void viewport(const QRectF &rect);
%End
%If (Qt_5_5_0 -)
    bool isAffine() const;
%End
};

QMatrix4x4 operator/(const QMatrix4x4 &matrix, float divisor);
QMatrix4x4 operator+(const QMatrix4x4 &m1, const QMatrix4x4 &m2);
QMatrix4x4 operator-(const QMatrix4x4 &m1, const QMatrix4x4 &m2);
QMatrix4x4 operator*(const QMatrix4x4 &m1, const QMatrix4x4 &m2) /__matmul__/;
QVector3D operator*(const QVector3D &vector, const QMatrix4x4 &matrix);
QVector3D operator*(const QMatrix4x4 &matrix, const QVector3D &vector);
QVector4D operator*(const QVector4D &vector, const QMatrix4x4 &matrix);
QVector4D operator*(const QMatrix4x4 &matrix, const QVector4D &vector);
QPoint operator*(const QPoint &point, const QMatrix4x4 &matrix);
QPointF operator*(const QPointF &point, const QMatrix4x4 &matrix);
QPoint operator*(const QMatrix4x4 &matrix, const QPoint &point);
QPointF operator*(const QMatrix4x4 &matrix, const QPointF &point);
QMatrix4x4 operator-(const QMatrix4x4 &matrix);
QMatrix4x4 operator*(float factor, const QMatrix4x4 &matrix);
QMatrix4x4 operator*(const QMatrix4x4 &matrix, float factor);
bool qFuzzyCompare(const QMatrix4x4 &m1, const QMatrix4x4 &m2);
QDataStream &operator<<(QDataStream &, const QMatrix4x4 & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix4x4 & /Constrained/) /ReleaseGIL/;

%ModuleHeaderCode
// Helpers for the matrix classes.
typedef float PYQT_FLOAT;

sipErrorState qtgui_matrixParseIndex(PyObject *tup, int nr_rows,
        int nr_columns, int *row, int *column);
sipErrorState qtgui_matrixDataFromSequence(PyObject *seq, int nr_values,
        PYQT_FLOAT *values);
sipErrorState qtgui_matrixDataAsList(int nr_values, const PYQT_FLOAT *values,
        PyObject **list);
%End

%ModuleCode
// Convert a Python object to a row and column.
sipErrorState qtgui_matrixParseIndex(PyObject *tup, int nr_rows,
        int nr_columns, int *row, int *column)
{
    sipErrorState es = sipErrorContinue;

    if (PyTuple_Check(tup) && PyArg_ParseTuple(tup, "ii", row, column))
        if (*row >= 0 && *row < nr_rows && *column >= 0 && *column < nr_columns)
            es = sipErrorNone;

    if (es == sipErrorContinue)
        PyErr_Format(PyExc_IndexError, "an index must be a row in the range 0 to %d and a column in the range 0 to %d", nr_rows - 1, nr_columns - 1);

    return es;
}


// Convert a Python object to an array of qreals.
sipErrorState qtgui_matrixDataFromSequence(PyObject *seq, int nr_values,
        PYQT_FLOAT *values)
{
    sipErrorState es;

    if (PySequence_Size(seq) == nr_values)
    {
        es = sipErrorNone;

        for (int i = 0; i < nr_values; ++i)
        {
            PyObject *value = PySequence_GetItem(seq, i);

            if (!value)
            {
                es = sipErrorFail;
                break;
            }

            PyErr_Clear();

            double d = PyFloat_AsDouble(value);

            if (PyErr_Occurred())
            {
                Py_DECREF(value);
                es = sipErrorContinue;
                break;
            }

            Py_DECREF(value);

            *values++ = d;
        }
    }
    else
    {
        es = sipErrorContinue;
    }

    if (es == sipErrorContinue)
        PyErr_Format(PyExc_TypeError, "a sequence of %d floats is expected",
                nr_values);

    return es;
}


// Convert an array of qreals to a Python list.
sipErrorState qtgui_matrixDataAsList(int nr_values, const PYQT_FLOAT *values,
        PyObject **list)
{
    PyObject *l = PyList_New(nr_values);

    if (!l)
        return sipErrorFail;

    for (int i = 0; i < nr_values; ++i)
    {
        PyObject *value = PyFloat_FromDouble(*values++);

        if (!value)
        {
            Py_DECREF(l);
            return sipErrorFail;
        }

        PyList_SetItem(l, i, value);
    }

    *list = l;

    return sipErrorNone;
}
%End
