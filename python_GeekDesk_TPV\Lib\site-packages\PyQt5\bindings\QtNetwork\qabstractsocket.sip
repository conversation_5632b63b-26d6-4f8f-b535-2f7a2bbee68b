// qabstractsocket.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractSocket : public QIODevice
{
%TypeHeaderCode
#include <qabstractsocket.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QNetworkReply, &sipType_QNetworkReply, -1, 1},
        {sipName_QHttpMultiPart, &sipType_QHttpMultiPart, -1, 2},
        {sipName_QAbstractNetworkCache, &sipType_QAbstractNetworkCache, 12, 3},
        {sipName_QNetworkConfigurationManager, &sipType_QNetworkConfigurationManager, -1, 4},
        {sipName_QNetworkCookieJar, &sipType_QNetworkCookieJar, -1, 5},
        {sipName_QAbstractSocket, &sipType_QAbstractSocket, 13, 6},
        {sipName_QLocalSocket, &sipType_QLocalSocket, -1, 7},
        {sipName_QDnsLookup, &sipType_QDnsLookup, -1, 8},
        {sipName_QNetworkSession, &sipType_QNetworkSession, -1, 9},
        {sipName_QTcpServer, &sipType_QTcpServer, -1, 10},
        {sipName_QNetworkAccessManager, &sipType_QNetworkAccessManager, -1, 11},
        {sipName_QLocalServer, &sipType_QLocalServer, -1, -1},
        {sipName_QNetworkDiskCache, &sipType_QNetworkDiskCache, -1, -1},
        {sipName_QUdpSocket, &sipType_QUdpSocket, -1, 14},
        {sipName_QTcpSocket, &sipType_QTcpSocket, 15, -1},
    #if defined(SIP_FEATURE_PyQt_SSL)
        {sipName_QSslSocket, &sipType_QSslSocket, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum SocketType
    {
        TcpSocket,
        UdpSocket,
%If (Qt_5_8_0 -)
        SctpSocket,
%End
        UnknownSocketType,
    };

    enum NetworkLayerProtocol
    {
        IPv4Protocol,
        IPv6Protocol,
        AnyIPProtocol,
        UnknownNetworkLayerProtocol,
    };

    enum SocketError
    {
        ConnectionRefusedError,
        RemoteHostClosedError,
        HostNotFoundError,
        SocketAccessError,
        SocketResourceError,
        SocketTimeoutError,
        DatagramTooLargeError,
        NetworkError,
        AddressInUseError,
        SocketAddressNotAvailableError,
        UnsupportedSocketOperationError,
        UnfinishedSocketOperationError,
        ProxyAuthenticationRequiredError,
        SslHandshakeFailedError,
        ProxyConnectionRefusedError,
        ProxyConnectionClosedError,
        ProxyConnectionTimeoutError,
        ProxyNotFoundError,
        ProxyProtocolError,
        OperationError,
        SslInternalError,
        SslInvalidUserDataError,
        TemporaryError,
        UnknownSocketError,
    };

    enum SocketState
    {
        UnconnectedState,
        HostLookupState,
        ConnectingState,
        ConnectedState,
        BoundState,
        ListeningState,
        ClosingState,
    };

    QAbstractSocket(QAbstractSocket::SocketType socketType, QObject *parent /TransferThis/);
    virtual ~QAbstractSocket();
    virtual void connectToHost(const QString &hostName, quint16 port, QIODevice::OpenMode mode = QIODevice::ReadWrite, QAbstractSocket::NetworkLayerProtocol protocol = QAbstractSocket::AnyIPProtocol) /ReleaseGIL/;
    virtual void connectToHost(const QHostAddress &address, quint16 port, QIODevice::OpenMode mode = QIODevice::ReadWrite) /ReleaseGIL/;
    virtual void disconnectFromHost() /ReleaseGIL/;
    bool isValid() const;
    virtual qint64 bytesAvailable() const;
    virtual qint64 bytesToWrite() const;
    virtual bool canReadLine() const;
    quint16 localPort() const;
    QHostAddress localAddress() const;
    quint16 peerPort() const;
    QHostAddress peerAddress() const;
    QString peerName() const;
    qint64 readBufferSize() const;
    virtual void setReadBufferSize(qint64 size);
    void abort();
    virtual bool setSocketDescriptor(qintptr socketDescriptor, QAbstractSocket::SocketState state = QAbstractSocket::ConnectedState, QIODevice::OpenMode mode = QIODevice::ReadWrite);
    virtual qintptr socketDescriptor() const;
    QAbstractSocket::SocketType socketType() const;
    QAbstractSocket::SocketState state() const;
    QAbstractSocket::SocketError error() const;
    virtual void close();
    virtual bool isSequential() const;
    virtual bool atEnd() const;
    bool flush() /ReleaseGIL/;
    virtual bool waitForConnected(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForReadyRead(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForBytesWritten(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForDisconnected(int msecs = 30000) /ReleaseGIL/;
    void setProxy(const QNetworkProxy &networkProxy);
    QNetworkProxy proxy() const;

signals:
    void hostFound();
    void connected();
    void disconnected();
    void stateChanged(QAbstractSocket::SocketState);
    void error(QAbstractSocket::SocketError);
%If (Qt_5_15_0 -)
    void errorOccurred(QAbstractSocket::SocketError);
%End
    void proxyAuthenticationRequired(const QNetworkProxy &proxy, QAuthenticator *authenticator);

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QAbstractSocket::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual SIP_PYOBJECT readLineData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QAbstractSocket::readLineData(s, a0) : sipCpp->readLineData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readLineData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 len /ArraySize/) /ReleaseGIL/;
    void setSocketState(QAbstractSocket::SocketState state);
    void setSocketError(QAbstractSocket::SocketError socketError);
    void setLocalPort(quint16 port);
    void setLocalAddress(const QHostAddress &address);
    void setPeerPort(quint16 port);
    void setPeerAddress(const QHostAddress &address);
    void setPeerName(const QString &name);

public:
    enum SocketOption
    {
        LowDelayOption,
        KeepAliveOption,
        MulticastTtlOption,
        MulticastLoopbackOption,
        TypeOfServiceOption,
%If (Qt_5_3_0 -)
        SendBufferSizeSocketOption,
%End
%If (Qt_5_3_0 -)
        ReceiveBufferSizeSocketOption,
%End
%If (Qt_5_11_0 -)
        PathMtuSocketOption,
%End
    };

    virtual void setSocketOption(QAbstractSocket::SocketOption option, const QVariant &value);
    virtual QVariant socketOption(QAbstractSocket::SocketOption option);

    enum BindFlag
    {
        DefaultForPlatform,
        ShareAddress,
        DontShareAddress,
        ReuseAddressHint,
    };

    typedef QFlags<QAbstractSocket::BindFlag> BindMode;

    enum PauseMode
    {
        PauseNever,
        PauseOnSslErrors,
    };

    typedef QFlags<QAbstractSocket::PauseMode> PauseModes;
    virtual void resume() /ReleaseGIL/;
    QAbstractSocket::PauseModes pauseMode() const;
    void setPauseMode(QAbstractSocket::PauseModes pauseMode);
    bool bind(const QHostAddress &address, quint16 port = 0, QAbstractSocket::BindMode mode = QAbstractSocket::DefaultForPlatform);
    bool bind(quint16 port = 0, QAbstractSocket::BindMode mode = QAbstractSocket::DefaultForPlatform);
%If (Qt_5_13_0 -)
    QString protocolTag() const;
%End
%If (Qt_5_13_0 -)
    void setProtocolTag(const QString &tag);
%End
};

QFlags<QAbstractSocket::BindFlag> operator|(QAbstractSocket::BindFlag f1, QFlags<QAbstractSocket::BindFlag> f2);
QFlags<QAbstractSocket::PauseMode> operator|(QAbstractSocket::PauseMode f1, QFlags<QAbstractSocket::PauseMode> f2);
