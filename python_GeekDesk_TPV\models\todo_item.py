#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
待辦事項模型
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from .base import BaseModel
from constants.enums import TodoTaskExecType


class TodoItem(BaseModel):
    """待辦事項模型"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 基本屬性
        self.title = kwargs.get('title', '')
        self.content = kwargs.get('content', '')
        self.priority = kwargs.get('priority', 1)  # 1-5, 5最高
        
        # 時間設置
        self.due_date = kwargs.get('due_date', None)
        self.reminder_time = kwargs.get('reminder_time', None)
        
        # 重複設置
        self.exec_type = TodoTaskExecType(kwargs.get('exec_type', TodoTaskExecType.ONCE))
        self.repeat_interval = kwargs.get('repeat_interval', 1)
        
        # 狀態
        self.completed = kwargs.get('completed', False)
        self.completed_at = kwargs.get('completed_at', None)
        
        # 分類和標籤
        self.category = kwargs.get('category', 'default')
        self.tags = kwargs.get('tags', [])
        
        # 提醒設置
        self.reminder_enabled = kwargs.get('reminder_enabled', True)
        self.reminder_sound = kwargs.get('reminder_sound', True)
        self.reminder_popup = kwargs.get('reminder_popup', True)
        
        # 統計
        self.reminder_count = kwargs.get('reminder_count', 0)
        self.last_reminded = kwargs.get('last_reminded', None)
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        base_dict = super().to_dict()
        base_dict.update({
            'title': self.title,
            'content': self.content,
            'priority': self.priority,
            'due_date': self.due_date,
            'reminder_time': self.reminder_time,
            'exec_type': self.exec_type.value,
            'repeat_interval': self.repeat_interval,
            'completed': self.completed,
            'completed_at': self.completed_at,
            'category': self.category,
            'tags': self.tags,
            'reminder_enabled': self.reminder_enabled,
            'reminder_sound': self.reminder_sound,
            'reminder_popup': self.reminder_popup,
            'reminder_count': self.reminder_count,
            'last_reminded': self.last_reminded
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TodoItem':
        """從字典創建實例"""
        # 處理日期時間字段
        for field in ['due_date', 'reminder_time', 'completed_at', 'last_reminded']:
            if field in data and isinstance(data[field], str):
                try:
                    data[field] = datetime.fromisoformat(data[field])
                except (ValueError, TypeError):
                    data[field] = None
        
        return cls(**data)
    
    def mark_completed(self):
        """標記為已完成"""
        self.completed = True
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()
    
    def mark_uncompleted(self):
        """標記為未完成"""
        self.completed = False
        self.completed_at = None
        self.updated_at = datetime.now()
    
    def is_overdue(self) -> bool:
        """檢查是否過期"""
        if not self.due_date or self.completed:
            return False
        return datetime.now() > self.due_date
    
    def is_due_today(self) -> bool:
        """檢查是否今天到期"""
        if not self.due_date or self.completed:
            return False
        today = datetime.now().date()
        return self.due_date.date() == today
    
    def is_due_soon(self, hours: int = 24) -> bool:
        """檢查是否即將到期"""
        if not self.due_date or self.completed:
            return False
        now = datetime.now()
        return now <= self.due_date <= now + timedelta(hours=hours)
    
    def should_remind(self) -> bool:
        """檢查是否應該提醒"""
        if not self.reminder_enabled or self.completed:
            return False
        
        if not self.reminder_time:
            return False
        
        now = datetime.now()
        
        # 檢查是否到了提醒時間
        if now < self.reminder_time:
            return False
        
        # 檢查是否已經提醒過（避免重複提醒）
        if self.last_reminded:
            # 如果是重複任務，檢查間隔
            if self.exec_type != TodoTaskExecType.ONCE:
                interval = self._get_repeat_interval_timedelta()
                if now - self.last_reminded < interval:
                    return False
            else:
                # 一次性任務，如果已經提醒過就不再提醒
                return False
        
        return True
    
    def get_next_reminder_time(self) -> Optional[datetime]:
        """獲取下次提醒時間"""
        if not self.reminder_enabled or self.completed:
            return None
        
        if self.exec_type == TodoTaskExecType.ONCE:
            return self.reminder_time
        
        if not self.last_reminded:
            return self.reminder_time
        
        # 計算下次提醒時間
        interval = self._get_repeat_interval_timedelta()
        return self.last_reminded + interval
    
    def _get_repeat_interval_timedelta(self) -> timedelta:
        """獲取重複間隔的timedelta對象"""
        if self.exec_type == TodoTaskExecType.DAILY:
            return timedelta(days=self.repeat_interval)
        elif self.exec_type == TodoTaskExecType.WEEKLY:
            return timedelta(weeks=self.repeat_interval)
        elif self.exec_type == TodoTaskExecType.MONTHLY:
            return timedelta(days=30 * self.repeat_interval)  # 近似
        elif self.exec_type == TodoTaskExecType.YEARLY:
            return timedelta(days=365 * self.repeat_interval)  # 近似
        else:
            return timedelta(0)
    
    def remind(self):
        """執行提醒"""
        self.reminder_count += 1
        self.last_reminded = datetime.now()
        self.updated_at = datetime.now()
    
    def add_tag(self, tag: str):
        """添加標籤"""
        if tag and tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.now()
    
    def remove_tag(self, tag: str):
        """移除標籤"""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.now()
    
    def has_tag(self, tag: str) -> bool:
        """檢查是否有指定標籤"""
        return tag in self.tags
    
    def matches_search(self, query: str) -> bool:
        """檢查是否匹配搜索查詢"""
        query = query.lower()
        
        # 檢查標題
        if query in self.title.lower():
            return True
        
        # 檢查內容
        if query in self.content.lower():
            return True
        
        # 檢查分類
        if query in self.category.lower():
            return True
        
        # 檢查標籤
        for tag in self.tags:
            if query in tag.lower():
                return True
        
        return False
    
    def get_priority_text(self) -> str:
        """獲取優先級文本"""
        priority_map = {
            1: "很低",
            2: "低",
            3: "中",
            4: "高",
            5: "很高"
        }
        return priority_map.get(self.priority, "中")
    
    def get_status_text(self) -> str:
        """獲取狀態文本"""
        if self.completed:
            return "已完成"
        elif self.is_overdue():
            return "已過期"
        elif self.is_due_today():
            return "今天到期"
        elif self.is_due_soon():
            return "即將到期"
        else:
            return "進行中"
