#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索管理器
"""

import os
import logging
import subprocess
from typing import List, Dict, Any, Optional, Callable
from pathlib import Path
import threading
import time

from PyQt5.QtCore import QObject, pyqtSignal, QThread, QTimer

from utils.config_manager import ConfigManager
from constants.enums import SearchType


class SearchResult:
    """搜索結果類"""
    
    def __init__(self, path: str, name: str, size: int = 0, 
                 modified_time: float = 0, result_type: str = "file"):
        self.path = path
        self.name = name
        self.size = size
        self.modified_time = modified_time
        self.result_type = result_type  # file, folder, application
        self.score = 0  # 相關性評分
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            'path': self.path,
            'name': self.name,
            'size': self.size,
            'modified_time': self.modified_time,
            'result_type': self.result_type,
            'score': self.score
        }


class SearchWorker(QThread):
    """搜索工作線程"""
    
    results_ready = pyqtSignal(list)  # 搜索結果就緒
    search_finished = pyqtSignal()    # 搜索完成
    
    def __init__(self, query: str, search_type: SearchType, max_results: int = 50):
        super().__init__()
        self.query = query
        self.search_type = search_type
        self.max_results = max_results
        self.logger = logging.getLogger(__name__)
        self._stop_flag = False
    
    def stop(self):
        """停止搜索"""
        self._stop_flag = True
    
    def run(self):
        """執行搜索"""
        try:
            results = []
            
            if self.search_type == SearchType.FILE_NAME:
                results = self._search_files()
            elif self.search_type == SearchType.EVERYTHING:
                results = self._search_everything()
            elif self.search_type == SearchType.FILE_CONTENT:
                results = self._search_content()
            
            if not self._stop_flag:
                self.results_ready.emit(results)
            
        except Exception as e:
            self.logger.error(f"搜索執行失敗: {e}", exc_info=True)
        finally:
            self.search_finished.emit()
    
    def _search_files(self) -> List[SearchResult]:
        """搜索文件名"""
        results = []
        query_lower = self.query.lower()
        
        # 搜索常用目錄
        search_paths = [
            Path.home() / "Desktop",
            Path.home() / "Documents",
            Path.home() / "Downloads",
            Path("C:/Program Files"),
            Path("C:/Program Files (x86)")
        ]
        
        for search_path in search_paths:
            if self._stop_flag or len(results) >= self.max_results:
                break
            
            try:
                if not search_path.exists():
                    continue
                
                for item in search_path.rglob("*"):
                    if self._stop_flag or len(results) >= self.max_results:
                        break
                    
                    if query_lower in item.name.lower():
                        try:
                            stat = item.stat()
                            result = SearchResult(
                                path=str(item),
                                name=item.name,
                                size=stat.st_size if item.is_file() else 0,
                                modified_time=stat.st_mtime,
                                result_type="file" if item.is_file() else "folder"
                            )
                            results.append(result)
                        except (OSError, PermissionError):
                            continue
                            
            except (OSError, PermissionError):
                continue
        
        return results
    
    def _search_everything(self) -> List[SearchResult]:
        """使用Everything搜索"""
        results = []
        
        try:
            # 檢查Everything是否可用
            if not self._is_everything_available():
                self.logger.warning("Everything不可用，回退到文件名搜索")
                return self._search_files()
            
            # 構建Everything命令
            cmd = [
                "es.exe",  # Everything命令行工具
                "-n", str(self.max_results),  # 最大結果數
                self.query
            ]
            
            # 執行搜索
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            stdout, stderr = process.communicate(timeout=10)
            
            if process.returncode == 0:
                # 解析結果
                for line in stdout.strip().split('\n'):
                    if self._stop_flag or len(results) >= self.max_results:
                        break
                    
                    if line.strip():
                        path = Path(line.strip())
                        if path.exists():
                            try:
                                stat = path.stat()
                                result = SearchResult(
                                    path=str(path),
                                    name=path.name,
                                    size=stat.st_size if path.is_file() else 0,
                                    modified_time=stat.st_mtime,
                                    result_type="file" if path.is_file() else "folder"
                                )
                                results.append(result)
                            except (OSError, PermissionError):
                                continue
            else:
                self.logger.error(f"Everything搜索失敗: {stderr}")
                return self._search_files()
                
        except Exception as e:
            self.logger.error(f"Everything搜索異常: {e}")
            return self._search_files()
        
        return results
    
    def _search_content(self) -> List[SearchResult]:
        """搜索文件內容"""
        results = []
        query_lower = self.query.lower()
        
        # 支持的文本文件擴展名
        text_extensions = {'.txt', '.md', '.py', '.js', '.html', '.css', '.xml', '.json', '.log'}
        
        # 搜索目錄
        search_paths = [
            Path.home() / "Documents",
            Path.home() / "Desktop"
        ]
        
        for search_path in search_paths:
            if self._stop_flag or len(results) >= self.max_results:
                break
            
            try:
                if not search_path.exists():
                    continue
                
                for item in search_path.rglob("*"):
                    if self._stop_flag or len(results) >= self.max_results:
                        break
                    
                    if item.is_file() and item.suffix.lower() in text_extensions:
                        try:
                            # 檢查文件大小（避免搜索過大的文件）
                            if item.stat().st_size > 10 * 1024 * 1024:  # 10MB
                                continue
                            
                            # 搜索文件內容
                            with open(item, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                if query_lower in content.lower():
                                    stat = item.stat()
                                    result = SearchResult(
                                        path=str(item),
                                        name=item.name,
                                        size=stat.st_size,
                                        modified_time=stat.st_mtime,
                                        result_type="file"
                                    )
                                    results.append(result)
                                    
                        except (OSError, PermissionError, UnicodeDecodeError):
                            continue
                            
            except (OSError, PermissionError):
                continue
        
        return results
    
    def _is_everything_available(self) -> bool:
        """檢查Everything是否可用"""
        try:
            # 檢查es.exe是否存在
            result = subprocess.run(
                ["es.exe", "-h"],
                capture_output=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False


class SearchManager(QObject):
    """搜索管理器"""
    
    # 信號定義
    search_started = pyqtSignal()
    search_results = pyqtSignal(list)  # 搜索結果
    search_finished = pyqtSignal()
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 搜索設置
        self.max_results = config_manager.get_setting("search.max_results", 50)
        self.search_delay = config_manager.get_setting("search.search_delay", 300)
        
        # 搜索工作線程
        self.search_worker: Optional[SearchWorker] = None
        
        # 搜索延遲定時器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._perform_search)
        
        # 當前搜索參數
        self._current_query = ""
        self._current_search_type = SearchType.FILE_NAME
        
        # 搜索歷史
        self.search_history: List[str] = []
        self.max_history = 20
    
    def search(self, query: str, search_type: SearchType = SearchType.FILE_NAME):
        """
        執行搜索
        
        Args:
            query: 搜索查詢
            search_type: 搜索類型
        """
        if not query.strip():
            self.search_results.emit([])
            return
        
        # 停止當前搜索
        self.stop_search()
        
        # 保存搜索參數
        self._current_query = query.strip()
        self._current_search_type = search_type
        
        # 添加到搜索歷史
        self._add_to_history(self._current_query)
        
        # 延遲搜索（防抖）
        self.search_timer.start(self.search_delay)
    
    def _perform_search(self):
        """執行實際搜索"""
        try:
            # 創建搜索工作線程
            self.search_worker = SearchWorker(
                self._current_query,
                self._current_search_type,
                self.max_results
            )
            
            # 連接信號
            self.search_worker.results_ready.connect(self._on_results_ready)
            self.search_worker.search_finished.connect(self._on_search_finished)
            
            # 發送搜索開始信號
            self.search_started.emit()
            
            # 啟動搜索
            self.search_worker.start()
            
            self.logger.debug(f"開始搜索: {self._current_query}")
            
        except Exception as e:
            self.logger.error(f"執行搜索失敗: {e}", exc_info=True)
            self.search_finished.emit()
    
    def _on_results_ready(self, results: List[SearchResult]):
        """搜索結果就緒"""
        # 按相關性排序
        sorted_results = self._sort_results(results)
        
        # 發送結果
        self.search_results.emit(sorted_results)
        
        self.logger.debug(f"搜索完成，找到 {len(results)} 個結果")
    
    def _on_search_finished(self):
        """搜索完成"""
        self.search_finished.emit()
        
        # 清理工作線程
        if self.search_worker:
            self.search_worker.deleteLater()
            self.search_worker = None
    
    def _sort_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """對搜索結果排序"""
        query_lower = self._current_query.lower()
        
        for result in results:
            score = 0
            name_lower = result.name.lower()
            
            # 完全匹配得分最高
            if name_lower == query_lower:
                score += 100
            # 開頭匹配
            elif name_lower.startswith(query_lower):
                score += 50
            # 包含匹配
            elif query_lower in name_lower:
                score += 20
            
            # 文件類型加分
            if result.result_type == "application":
                score += 10
            elif result.result_type == "folder":
                score += 5
            
            result.score = score
        
        # 按分數降序排序
        return sorted(results, key=lambda x: x.score, reverse=True)
    
    def stop_search(self):
        """停止當前搜索"""
        # 停止定時器
        if self.search_timer.isActive():
            self.search_timer.stop()
        
        # 停止工作線程
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.stop()
            self.search_worker.wait(1000)  # 等待1秒
            if self.search_worker.isRunning():
                self.search_worker.terminate()
    
    def _add_to_history(self, query: str):
        """添加到搜索歷史"""
        if query in self.search_history:
            self.search_history.remove(query)
        
        self.search_history.insert(0, query)
        
        # 限制歷史記錄數量
        if len(self.search_history) > self.max_history:
            self.search_history = self.search_history[:self.max_history]
    
    def get_search_history(self) -> List[str]:
        """獲取搜索歷史"""
        return self.search_history.copy()
    
    def clear_search_history(self):
        """清空搜索歷史"""
        self.search_history.clear()
    
    def is_everything_available(self) -> bool:
        """檢查Everything是否可用"""
        try:
            result = subprocess.run(
                ["es.exe", "-h"],
                capture_output=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False
    
    def cleanup(self):
        """清理資源"""
        self.stop_search()
        
        if self.search_worker:
            self.search_worker.deleteLater()
            self.search_worker = None
