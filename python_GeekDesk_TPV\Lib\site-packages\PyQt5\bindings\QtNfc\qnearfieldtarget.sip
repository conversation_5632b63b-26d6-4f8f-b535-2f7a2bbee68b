// qnearfieldtarget.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QNearFieldTarget : public QObject
{
%TypeHeaderCode
#include <qnearfieldtarget.h>
%End

public:
    enum Type
    {
        ProprietaryTag,
        NfcTagType1,
        NfcTagType2,
        NfcTagType3,
        NfcTagType4,
        MifareTag,
    };

    enum AccessMethod
    {
        UnknownAccess,
        NdefAccess,
        TagTypeSpecificAccess,
        LlcpAccess,
    };

    typedef QFlags<QNearFieldTarget::AccessMethod> AccessMethods;

    enum Error
    {
        NoError,
        UnknownError,
        UnsupportedError,
        TargetOutOfRangeError,
        NoResponseError,
        ChecksumMismatchError,
        InvalidParametersError,
        NdefReadError,
        NdefWriteError,
%If (Qt_5_9_0 -)
        CommandError,
%End
    };

    class RequestId
    {
%TypeHeaderCode
#include <qnearfieldtarget.h>
%End

    public:
        RequestId();
        RequestId(const QNearFieldTarget::RequestId &other);
        ~RequestId();
        bool isValid() const;
        int refCount() const;
        bool operator<(const QNearFieldTarget::RequestId &other) const;
        bool operator==(const QNearFieldTarget::RequestId &other) const;
        bool operator!=(const QNearFieldTarget::RequestId &other) const;
    };

    explicit QNearFieldTarget(QObject *parent /TransferThis/ = 0);
    virtual ~QNearFieldTarget();
    virtual QByteArray uid() const = 0;
    virtual QUrl url() const;
    virtual QNearFieldTarget::Type type() const = 0;
    virtual QNearFieldTarget::AccessMethods accessMethods() const = 0;
    bool isProcessingCommand() const;
    virtual bool hasNdefMessage();
    virtual QNearFieldTarget::RequestId readNdefMessages();
    virtual QNearFieldTarget::RequestId writeNdefMessages(const QList<QNdefMessage> &messages);
    virtual QNearFieldTarget::RequestId sendCommand(const QByteArray &command);
    virtual QNearFieldTarget::RequestId sendCommands(const QList<QByteArray> &commands);
    virtual bool waitForRequestCompleted(const QNearFieldTarget::RequestId &id, int msecs = 5000) /ReleaseGIL/;
    QVariant requestResponse(const QNearFieldTarget::RequestId &id);
    void setResponseForRequest(const QNearFieldTarget::RequestId &id, const QVariant &response, bool emitRequestCompleted = true);

protected:
    virtual bool handleResponse(const QNearFieldTarget::RequestId &id, const QByteArray &response);
%If (Qt_5_12_0 -)
    void reportError(QNearFieldTarget::Error error, const QNearFieldTarget::RequestId &id);
%End

signals:
    void disconnected();
    void ndefMessageRead(const QNdefMessage &message);
    void ndefMessagesWritten();
    void requestCompleted(const QNearFieldTarget::RequestId &id);
    void error(QNearFieldTarget::Error error, const QNearFieldTarget::RequestId &id);

public:
%If (Qt_5_9_0 -)
    bool keepConnection() const;
%End
%If (Qt_5_9_0 -)
    bool setKeepConnection(bool isPersistent);
%End
%If (Qt_5_9_0 -)
    bool disconnect();
%End
%If (Qt_5_9_0 -)
    int maxCommandLength() const;
%End
};

%End
%If (Qt_5_5_0 -)
QFlags<QNearFieldTarget::AccessMethod> operator|(QNearFieldTarget::AccessMethod f1, QFlags<QNearFieldTarget::AccessMethod> f2);
%End
