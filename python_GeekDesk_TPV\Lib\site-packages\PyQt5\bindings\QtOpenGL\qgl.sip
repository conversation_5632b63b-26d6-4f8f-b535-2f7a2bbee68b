// qgl.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_OpenGL)

namespace QGL
{
%TypeHeaderCode
#include <qgl.h>
%End

    enum FormatOption
    {
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON>g<PERSON>,
        AlphaChan<PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>cil<PERSON>uffer,
        StereoBuffers,
        DirectRendering,
        HasOverlay,
        SampleBuffers,
        SingleBuffer,
        NoDepthBuffer,
        ColorIndex,
        NoAlphaChannel,
        NoAccumBuffer,
        NoStencilBuffer,
        NoStereoBuffers,
        IndirectRendering,
        NoOverlay,
        NoSampleBuffers,
        DeprecatedFunctions,
        NoDeprecatedFunctions,
    };

    typedef QFlags<QGL::FormatOption> FormatOptions;
};

%End
%If (PyQt_OpenGL)
QFlags<QGL::FormatOption> operator|(QGL::FormatOption f1, QFlags<QGL::FormatOption> f2);
%End
%If (PyQt_OpenGL)

class QGLFormat
{
%TypeHeaderCode
#include <qgl.h>
%End

public:
    enum OpenGLVersionFlag
    {
        OpenGL_Version_None,
        OpenGL_Version_1_1,
        OpenGL_Version_1_2,
        OpenGL_Version_1_3,
        OpenGL_Version_1_4,
        OpenGL_Version_1_5,
        OpenGL_Version_2_0,
        OpenGL_Version_2_1,
        OpenGL_Version_3_0,
        OpenGL_Version_3_1,
        OpenGL_Version_3_2,
        OpenGL_Version_3_3,
        OpenGL_Version_4_0,
        OpenGL_Version_4_1,
        OpenGL_Version_4_2,
        OpenGL_Version_4_3,
        OpenGL_ES_Common_Version_1_0,
        OpenGL_ES_CommonLite_Version_1_0,
        OpenGL_ES_Common_Version_1_1,
        OpenGL_ES_CommonLite_Version_1_1,
        OpenGL_ES_Version_2_0,
    };

    typedef QFlags<QGLFormat::OpenGLVersionFlag> OpenGLVersionFlags;
    QGLFormat();
    QGLFormat(QGL::FormatOptions options, int plane = 0);
    QGLFormat(const QGLFormat &other);
    ~QGLFormat();
    void setDepthBufferSize(int size);
    int depthBufferSize() const;
    void setAccumBufferSize(int size);
    int accumBufferSize() const;
    void setAlphaBufferSize(int size);
    int alphaBufferSize() const;
    void setStencilBufferSize(int size);
    int stencilBufferSize() const;
    void setSampleBuffers(bool enable);
    void setSamples(int numSamples);
    int samples() const;
    void setDoubleBuffer(bool enable);
    void setDepth(bool enable);
    void setRgba(bool enable);
    void setAlpha(bool enable);
    void setAccum(bool enable);
    void setStencil(bool enable);
    void setStereo(bool enable);
    void setDirectRendering(bool enable);
    void setOverlay(bool enable);
    int plane() const;
    void setPlane(int plane);
    void setOption(QGL::FormatOptions opt);
    bool testOption(QGL::FormatOptions opt) const;
    static QGLFormat defaultFormat();
    static void setDefaultFormat(const QGLFormat &f);
    static QGLFormat defaultOverlayFormat();
    static void setDefaultOverlayFormat(const QGLFormat &f);
    static bool hasOpenGL();
    static bool hasOpenGLOverlays();
    bool doubleBuffer() const;
    bool depth() const;
    bool rgba() const;
    bool alpha() const;
    bool accum() const;
    bool stencil() const;
    bool stereo() const;
    bool directRendering() const;
    bool hasOverlay() const;
    bool sampleBuffers() const;
    void setRedBufferSize(int size);
    int redBufferSize() const;
    void setGreenBufferSize(int size);
    int greenBufferSize() const;
    void setBlueBufferSize(int size);
    int blueBufferSize() const;
    void setSwapInterval(int interval);
    int swapInterval() const;
    static QGLFormat::OpenGLVersionFlags openGLVersionFlags();
    void setVersion(int major, int minor);
    int majorVersion() const;
    int minorVersion() const;

    enum OpenGLContextProfile
    {
        NoProfile,
        CoreProfile,
        CompatibilityProfile,
    };

    void setProfile(QGLFormat::OpenGLContextProfile profile);
    QGLFormat::OpenGLContextProfile profile() const;
};

%End
%If (PyQt_OpenGL)
bool operator==(const QGLFormat &, const QGLFormat &);
%End
%If (PyQt_OpenGL)
bool operator!=(const QGLFormat &, const QGLFormat &);
%End
%If (PyQt_OpenGL)

class QGLContext /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qgl.h>
%End

public:
    QGLContext(const QGLFormat &format);
    virtual ~QGLContext();
    virtual bool create(const QGLContext *shareContext = 0);
    bool isValid() const;
    bool isSharing() const;
    void reset();
    QGLFormat format() const;
    QGLFormat requestedFormat() const;
    void setFormat(const QGLFormat &format);
    virtual void makeCurrent();
    virtual void doneCurrent();
    virtual void swapBuffers() const;
    GLuint bindTexture(const QImage &image, GLenum target = GL_TEXTURE_2D, GLint format = GL_RGBA);
    GLuint bindTexture(const QPixmap &pixmap, GLenum target = GL_TEXTURE_2D, GLint format = GL_RGBA);
    void drawTexture(const QRectF &target, GLuint textureId, GLenum textureTarget = GL_TEXTURE_2D);
    void drawTexture(const QPointF &point, GLuint textureId, GLenum textureTarget = GL_TEXTURE_2D);
    GLuint bindTexture(const QString &fileName);
    void deleteTexture(GLuint tx_id);
    static void setTextureCacheLimit(int size);
    static int textureCacheLimit();
    QFunctionPointer getProcAddress(const QString &proc) const;
    QPaintDevice *device() const;
    QColor overlayTransparentColor() const;
    static const QGLContext *currentContext();

protected:
    virtual bool chooseContext(const QGLContext *shareContext = 0);
    bool deviceIsPixmap() const;
    bool windowCreated() const;
    void setWindowCreated(bool on);
    bool initialized() const;
    void setInitialized(bool on);

public:
    static bool areSharing(const QGLContext *context1, const QGLContext *context2);

    enum BindOption
    {
        NoBindOption,
        InvertedYBindOption,
        MipmapBindOption,
        PremultipliedAlphaBindOption,
        LinearFilteringBindOption,
        DefaultBindOption,
    };

    typedef QFlags<QGLContext::BindOption> BindOptions;
    GLuint bindTexture(const QImage &image, GLenum target, GLint format, QGLContext::BindOptions options);
    GLuint bindTexture(const QPixmap &pixmap, GLenum target, GLint format, QGLContext::BindOptions options);
    void moveToThread(QThread *thread);

private:
    QGLContext(const QGLContext &);
};

%End
%If (PyQt_OpenGL)

class QGLWidget : public QWidget
{
%TypeHeaderCode
#include <qgl.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QGLWidget, &sipType_QGLWidget, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QGLWidget(QWidget *parent /TransferThis/ = 0, const QGLWidget *shareWidget = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    QGLWidget(QGLContext *context /Transfer/, QWidget *parent /TransferThis/ = 0, const QGLWidget *shareWidget = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    QGLWidget(const QGLFormat &format, QWidget *parent /TransferThis/ = 0, const QGLWidget *shareWidget = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QGLWidget();
    void qglColor(const QColor &c) const;
    void qglClearColor(const QColor &c) const;
    bool isValid() const;
    bool isSharing() const;
    void makeCurrent();
    void doneCurrent();
    bool doubleBuffer() const;
    void swapBuffers();
    QGLFormat format() const;
    QGLContext *context() const;
    void setContext(QGLContext *context /Transfer/, const QGLContext *shareContext = 0, bool deleteOldContext = true);
    QPixmap renderPixmap(int width = 0, int height = 0, bool useContext = false);
    QImage grabFrameBuffer(bool withAlpha = false);
    void makeOverlayCurrent();
    const QGLContext *overlayContext() const;
    static QImage convertToGLFormat(const QImage &img);
    void renderText(int x, int y, const QString &str, const QFont &font = QFont());
    void renderText(double x, double y, double z, const QString &str, const QFont &font = QFont());
    virtual QPaintEngine *paintEngine() const;
    GLuint bindTexture(const QImage &image, GLenum target = GL_TEXTURE_2D, GLint format = GL_RGBA);
    GLuint bindTexture(const QPixmap &pixmap, GLenum target = GL_TEXTURE_2D, GLint format = GL_RGBA);
    GLuint bindTexture(const QString &fileName);
    void drawTexture(const QRectF &target, GLuint textureId, GLenum textureTarget = GL_TEXTURE_2D);
    void drawTexture(const QPointF &point, GLuint textureId, GLenum textureTarget = GL_TEXTURE_2D);
    void deleteTexture(GLuint tx_id);

public slots:
    virtual void updateGL();
    virtual void updateOverlayGL();

protected:
    virtual bool event(QEvent *);
    virtual void initializeGL();
    virtual void resizeGL(int w, int h);
    virtual void paintGL();
    virtual void initializeOverlayGL();
    virtual void resizeOverlayGL(int w, int h);
    virtual void paintOverlayGL();
    void setAutoBufferSwap(bool on);
    bool autoBufferSwap() const;
    virtual void paintEvent(QPaintEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void glInit();
    virtual void glDraw();

public:
    GLuint bindTexture(const QImage &image, GLenum target, GLint format, QGLContext::BindOptions options);
    GLuint bindTexture(const QPixmap &pixmap, GLenum target, GLint format, QGLContext::BindOptions options);
};

%End
%If (PyQt_OpenGL)
QFlags<QGLFormat::OpenGLVersionFlag> operator|(QGLFormat::OpenGLVersionFlag f1, QFlags<QGLFormat::OpenGLVersionFlag> f2);
%End
%If (PyQt_OpenGL)
QFlags<QGLContext::BindOption> operator|(QGLContext::BindOption f1, QFlags<QGLContext::BindOption> f2);
%End
