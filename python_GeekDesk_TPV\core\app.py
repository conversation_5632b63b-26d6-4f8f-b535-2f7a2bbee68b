#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主應用程序類
"""

import sys
import logging
from typing import Optional

from PyQt5.QtWidgets import (QApplication, QSystemTrayIcon, QMenu, QAction, 
                            QMessageBox, QWidget)
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QIcon, QPixmap

from views.main_window import MainWindow
from views.settings_window import SettingsWindow
from views.todo_window import TodoWindow
from utils.hotkey_manager import HotkeyManager
from utils.tray_manager import TrayManager
from utils.search_manager import SearchManager
from utils.config_manager import ConfigManager
from constants.constants import APP_NAME, ICONS_DIR
from constants.enums import WindowState


class GeekDeskApp(QObject):
    """主應用程序類"""
    
    # 信號定義
    show_main_window = pyqtSignal()
    show_settings_window = pyqtSignal()
    show_todo_window = pyqtSignal()
    quit_application = pyqtSignal()
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 窗口實例
        self.main_window: Optional[MainWindow] = None
        self.settings_window: Optional[SettingsWindow] = None
        self.todo_window: Optional[TodoWindow] = None
        
        # 管理器實例
        self.hotkey_manager: Optional[HotkeyManager] = None
        self.tray_manager: Optional[TrayManager] = None
        self.search_manager: Optional[SearchManager] = None
        
        # 系統托盤
        self.tray_icon: Optional[QSystemTrayIcon] = None
        self.tray_menu: Optional[QMenu] = None
        
        # 初始化應用程序
        self._init_application()
    
    def _init_application(self):
        """初始化應用程序"""
        try:
            self.logger.info("初始化應用程序...")
            
            # 初始化管理器
            self._init_managers()
            
            # 初始化系統托盤
            self._init_system_tray()
            
            # 初始化窗口
            self._init_windows()
            
            # 連接信號
            self._connect_signals()
            
            # 註冊熱鍵
            self._register_hotkeys()
            
            self.logger.info("應用程序初始化完成")
            
        except Exception as e:
            self.logger.error(f"應用程序初始化失敗: {e}", exc_info=True)
            raise
    
    def _init_managers(self):
        """初始化管理器"""
        self.hotkey_manager = HotkeyManager(self.config_manager)
        self.tray_manager = TrayManager(self.config_manager)
        self.search_manager = SearchManager(self.config_manager)
    
    def _init_system_tray(self):
        """初始化系統托盤"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            self.logger.error("系統托盤不可用")
            return
        
        # 創建托盤圖標
        icon_path = ICONS_DIR / "logo.ico"
        if icon_path.exists():
            icon = QIcon(str(icon_path))
        else:
            icon = QIcon()
        
        self.tray_icon = QSystemTrayIcon(icon)
        self.tray_icon.setToolTip(APP_NAME)
        
        # 創建托盤菜單
        self._create_tray_menu()
        
        # 顯示托盤圖標
        self.tray_icon.show()
        
        self.logger.info("系統托盤初始化完成")
    
    def _create_tray_menu(self):
        """創建托盤菜單"""
        self.tray_menu = QMenu()
        
        # 顯示主面板
        show_action = QAction("顯示主面板", self)
        show_action.triggered.connect(self.show_main_panel)
        self.tray_menu.addAction(show_action)
        
        # 待辦事項
        todo_action = QAction("待辦事項", self)
        todo_action.triggered.connect(self.show_todo_panel)
        self.tray_menu.addAction(todo_action)
        
        self.tray_menu.addSeparator()
        
        # 設置
        settings_action = QAction("設置", self)
        settings_action.triggered.connect(self.show_settings_panel)
        self.tray_menu.addAction(settings_action)
        
        self.tray_menu.addSeparator()
        
        # 退出
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_app)
        self.tray_menu.addAction(quit_action)
        
        # 設置托盤菜單
        self.tray_icon.setContextMenu(self.tray_menu)
        
        # 連接托盤圖標點擊事件
        self.tray_icon.activated.connect(self._on_tray_activated)
    
    def _init_windows(self):
        """初始化窗口"""
        # 主窗口將在需要時創建
        pass
    
    def _connect_signals(self):
        """連接信號"""
        self.show_main_window.connect(self.show_main_panel)
        self.show_settings_window.connect(self.show_settings_panel)
        self.show_todo_window.connect(self.show_todo_panel)
        self.quit_application.connect(self.quit_app)
    
    def _register_hotkeys(self):
        """註冊熱鍵"""
        if self.hotkey_manager:
            # 主面板熱鍵
            main_hotkey = self.config_manager.get_setting("hotkeys.main", "ctrl+space")
            self.hotkey_manager.register_hotkey(
                "main_panel", 
                main_hotkey, 
                self.show_main_panel
            )
            
            # 待辦熱鍵
            todo_hotkey = self.config_manager.get_setting("hotkeys.todo", "ctrl+shift+t")
            self.hotkey_manager.register_hotkey(
                "todo_panel",
                todo_hotkey,
                self.show_todo_panel
            )
            
            self.logger.info("熱鍵註冊完成")
    
    def _on_tray_activated(self, reason):
        """托盤圖標激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show_main_panel()
        elif reason == QSystemTrayIcon.MiddleClick:
            self.show_main_panel()
    
    def show_main_panel(self):
        """顯示主面板"""
        try:
            if not self.main_window:
                self.main_window = MainWindow(
                    self.config_manager,
                    self.search_manager
                )
            
            # 顯示窗口
            if self.main_window.isHidden():
                self.main_window.show()
            
            # 激活窗口
            self.main_window.activateWindow()
            self.main_window.raise_()
            
            self.logger.debug("顯示主面板")
            
        except Exception as e:
            self.logger.error(f"顯示主面板失敗: {e}", exc_info=True)
    
    def show_settings_panel(self):
        """顯示設置面板"""
        try:
            if not self.settings_window:
                self.settings_window = SettingsWindow(self.config_manager)
            
            self.settings_window.show()
            self.settings_window.activateWindow()
            self.settings_window.raise_()
            
            self.logger.debug("顯示設置面板")
            
        except Exception as e:
            self.logger.error(f"顯示設置面板失敗: {e}", exc_info=True)
    
    def show_todo_panel(self):
        """顯示待辦面板"""
        try:
            if not self.todo_window:
                self.todo_window = TodoWindow(self.config_manager)
            
            self.todo_window.show()
            self.todo_window.activateWindow()
            self.todo_window.raise_()
            
            self.logger.debug("顯示待辦面板")
            
        except Exception as e:
            self.logger.error(f"顯示待辦面板失敗: {e}", exc_info=True)
    
    def quit_app(self):
        """退出應用程序"""
        try:
            self.logger.info("正在退出應用程序...")
            
            # 註銷熱鍵
            if self.hotkey_manager:
                self.hotkey_manager.unregister_all()
            
            # 隱藏托盤圖標
            if self.tray_icon:
                self.tray_icon.hide()
            
            # 關閉所有窗口
            if self.main_window:
                self.main_window.close()
            if self.settings_window:
                self.settings_window.close()
            if self.todo_window:
                self.todo_window.close()
            
            # 退出應用程序
            QApplication.quit()
            
        except Exception as e:
            self.logger.error(f"退出應用程序失敗: {e}", exc_info=True)
