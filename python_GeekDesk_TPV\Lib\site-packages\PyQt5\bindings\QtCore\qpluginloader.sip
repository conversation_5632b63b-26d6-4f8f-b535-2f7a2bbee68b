// qpluginloader.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPluginLoader : public QObject
{
%TypeHeaderCode
#include <qpluginloader.h>
%End

public:
    explicit QPluginLoader(QObject *parent /TransferThis/ = 0);
    QPluginLoader(const QString &fileName, QObject *parent /TransferThis/ = 0);
    virtual ~QPluginLoader();
    QObject *instance();
    static QObjectList staticInstances();
    bool load();
    bool unload();
    bool isLoaded() const;
    void setFileName(const QString &fileName);
    QString fileName() const;
    QString errorString() const;
    void setLoadHints(QLibrary::LoadHints loadHints);
    QLibrary::LoadHints loadHints() const;
};
