// qfontdatabase.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFontDatabase
{
%TypeHeaderCode
#include <qfontdatabase.h>
%End

public:
    enum WritingSystem
    {
        Any,
        Latin,
        Greek,
        Cyrillic,
        Armenian,
        Hebrew,
        Arabic,
        Syriac,
        Thaana,
        Devanagari,
        Bengali,
        Gurmukhi,
        Gujarati,
        Oriya,
        Tamil,
        Telugu,
        Kannada,
        Malayalam,
        Sinhala,
        Thai,
        Lao,
        Tibetan,
        Myanmar,
        Georgian,
        Khmer,
        SimplifiedChinese,
        TraditionalChinese,
        Japanese,
        Korean,
        Vietnamese,
        Other,
        Symbol,
        Ogham,
        Runic,
        Nko,
    };

    static QList<int> standardSizes();
    QFontDatabase();
    QList<QFontDatabase::WritingSystem> writingSystems() const;
    QList<QFontDatabase::WritingSystem> writingSystems(const QString &family) const;
    QStringList families(QFontDatabase::WritingSystem writingSystem = QFontDatabase::Any) const;
    QStringList styles(const QString &family) const;
    QList<int> pointSizes(const QString &family, const QString &style = QString());
    QList<int> smoothSizes(const QString &family, const QString &style);
    QString styleString(const QFont &font);
    QString styleString(const QFontInfo &fontInfo);
    QFont font(const QString &family, const QString &style, int pointSize) const;
    bool isBitmapScalable(const QString &family, const QString &style = QString()) const;
    bool isSmoothlyScalable(const QString &family, const QString &style = QString()) const;
    bool isScalable(const QString &family, const QString &style = QString()) const;
    bool isFixedPitch(const QString &family, const QString &style = QString()) const;
    bool italic(const QString &family, const QString &style) const;
    bool bold(const QString &family, const QString &style) const;
    int weight(const QString &family, const QString &style) const;
    static QString writingSystemName(QFontDatabase::WritingSystem writingSystem);
    static QString writingSystemSample(QFontDatabase::WritingSystem writingSystem);
    static int addApplicationFont(const QString &fileName);
    static int addApplicationFontFromData(const QByteArray &fontData);
    static QStringList applicationFontFamilies(int id);
    static bool removeApplicationFont(int id);
    static bool removeAllApplicationFonts();
    static bool supportsThreadedFontRendering();
%If (Qt_5_2_0 -)

    enum SystemFont
    {
        GeneralFont,
        FixedFont,
        TitleFont,
        SmallestReadableFont,
    };

%End
%If (Qt_5_2_0 -)
    static QFont systemFont(QFontDatabase::SystemFont type);
%End
%If (Qt_5_5_0 -)
    bool isPrivateFamily(const QString &family) const;
%End
};
