// qsurfaceformat.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSurfaceFormat
{
%TypeHeaderCode
#include <qsurfaceformat.h>
%End

public:
    enum FormatOption
    {
        StereoBuffers,
        DebugContext,
        DeprecatedFunctions,
%If (Qt_5_5_0 -)
        ResetNotification,
%End
    };

    typedef QFlags<QSurfaceFormat::FormatOption> FormatOptions;

    enum SwapBehavior
    {
        DefaultSwapBehavior,
        SingleBuffer,
        DoubleBuffer,
        TripleBuffer,
    };

    enum RenderableType
    {
        DefaultRenderableType,
        OpenGL,
        OpenGLES,
        OpenVG,
    };

    enum OpenGLContextProfile
    {
        NoProfile,
        CoreProfile,
        CompatibilityProfile,
    };

    QSurfaceFormat();
    QSurfaceFormat(QSurfaceFormat::FormatOptions options);
    QSurfaceFormat(const QSurfaceFormat &other);
    ~QSurfaceFormat();
    void setDepthBufferSize(int size);
    int depthBufferSize() const;
    void setStencilBufferSize(int size);
    int stencilBufferSize() const;
    void setRedBufferSize(int size);
    int redBufferSize() const;
    void setGreenBufferSize(int size);
    int greenBufferSize() const;
    void setBlueBufferSize(int size);
    int blueBufferSize() const;
    void setAlphaBufferSize(int size);
    int alphaBufferSize() const;
    void setSamples(int numSamples);
    int samples() const;
    void setSwapBehavior(QSurfaceFormat::SwapBehavior behavior);
    QSurfaceFormat::SwapBehavior swapBehavior() const;
    bool hasAlpha() const;
    void setProfile(QSurfaceFormat::OpenGLContextProfile profile);
    QSurfaceFormat::OpenGLContextProfile profile() const;
    void setRenderableType(QSurfaceFormat::RenderableType type);
    QSurfaceFormat::RenderableType renderableType() const;
    void setMajorVersion(int majorVersion);
    int majorVersion() const;
    void setMinorVersion(int minorVersion);
    int minorVersion() const;
    void setStereo(bool enable);
    void setOption(QSurfaceFormat::FormatOptions opt);
    bool testOption(QSurfaceFormat::FormatOptions opt) const;
    bool stereo() const;
%If (Qt_5_1_0 -)
    QPair<int, int> version() const;
%End
%If (Qt_5_1_0 -)
    void setVersion(int major, int minor);
%End
%If (Qt_5_3_0 -)
    void setOptions(QSurfaceFormat::FormatOptions options);
%End
%If (Qt_5_3_0 -)
    void setOption(QSurfaceFormat::FormatOption option, bool on = true);
%End
%If (Qt_5_3_0 -)
    bool testOption(QSurfaceFormat::FormatOption option) const;
%End
%If (Qt_5_3_0 -)
    QSurfaceFormat::FormatOptions options() const;
%End
%If (Qt_5_3_0 -)
    int swapInterval() const;
%End
%If (Qt_5_3_0 -)
    void setSwapInterval(int interval);
%End
%If (Qt_5_4_0 -)
    static void setDefaultFormat(const QSurfaceFormat &format);
%End
%If (Qt_5_4_0 -)
    static QSurfaceFormat defaultFormat();
%End
%If (Qt_5_10_0 -)

    enum ColorSpace
    {
        DefaultColorSpace,
        sRGBColorSpace,
    };

%End
%If (Qt_5_10_0 -)
    QSurfaceFormat::ColorSpace colorSpace() const;
%End
%If (Qt_5_10_0 -)
    void setColorSpace(QSurfaceFormat::ColorSpace colorSpace);
%End
};

bool operator==(const QSurfaceFormat &, const QSurfaceFormat &);
bool operator!=(const QSurfaceFormat &, const QSurfaceFormat &);
QFlags<QSurfaceFormat::FormatOption> operator|(QSurfaceFormat::FormatOption f1, QFlags<QSurfaceFormat::FormatOption> f2);
