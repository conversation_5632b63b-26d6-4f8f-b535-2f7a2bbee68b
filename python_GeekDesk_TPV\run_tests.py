#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試運行腳本
"""

import sys
import subprocess
from pathlib import Path


def run_tests():
    """運行測試"""
    print("python_GeekDesk_TPV 測試運行器")
    print("=" * 50)
    
    # 檢查 pytest 是否安裝
    try:
        import pytest
    except ImportError:
        print("錯誤: pytest 未安裝")
        print("請運行: pip install pytest pytest-qt")
        return False
    
    # 運行測試
    test_args = [
        "-v",                    # 詳細輸出
        "--tb=short",           # 簡短的錯誤回溯
        "--color=yes",          # 彩色輸出
        "tests/",               # 測試目錄
    ]
    
    # 如果有命令行參數，添加到測試參數中
    if len(sys.argv) > 1:
        test_args.extend(sys.argv[1:])
    
    print(f"運行命令: pytest {' '.join(test_args)}")
    print("-" * 50)
    
    # 執行測試
    exit_code = pytest.main(test_args)
    
    if exit_code == 0:
        print("\n✅ 所有測試通過！")
    else:
        print(f"\n❌ 測試失敗，退出代碼: {exit_code}")
    
    return exit_code == 0


def run_coverage():
    """運行測試覆蓋率"""
    print("運行測試覆蓋率分析...")
    
    try:
        import coverage
    except ImportError:
        print("錯誤: coverage 未安裝")
        print("請運行: pip install coverage")
        return False
    
    # 運行覆蓋率測試
    cmd = [
        "coverage", "run", 
        "--source=.", 
        "-m", "pytest", 
        "tests/"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        # 生成覆蓋率報告
        subprocess.run(["coverage", "report"])
        subprocess.run(["coverage", "html"])
        print("\n📊 覆蓋率報告已生成到 htmlcov/ 目錄")
        return True
    else:
        print(f"覆蓋率測試失敗: {result.stderr}")
        return False


def run_linting():
    """運行代碼檢查"""
    print("運行代碼檢查...")
    
    # 檢查 flake8
    try:
        result = subprocess.run(
            ["flake8", ".", "--max-line-length=100", "--exclude=tests,build,dist"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ flake8 檢查通過")
        else:
            print("❌ flake8 檢查失敗:")
            print(result.stdout)
            
    except FileNotFoundError:
        print("⚠️  flake8 未安裝，跳過檢查")
    
    # 檢查 black
    try:
        result = subprocess.run(
            ["black", "--check", "--diff", "."],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ black 格式檢查通過")
        else:
            print("❌ black 格式檢查失敗:")
            print(result.stdout)
            
    except FileNotFoundError:
        print("⚠️  black 未安裝，跳過檢查")


def main():
    """主函數"""
    if len(sys.argv) > 1 and sys.argv[1] == "--coverage":
        # 運行覆蓋率測試
        success = run_coverage()
    elif len(sys.argv) > 1 and sys.argv[1] == "--lint":
        # 運行代碼檢查
        run_linting()
        success = True
    elif len(sys.argv) > 1 and sys.argv[1] == "--all":
        # 運行所有檢查
        print("運行完整測試套件...")
        run_linting()
        success = run_coverage()
    else:
        # 運行基本測試
        success = run_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
