// qcborstream.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_12_0 -)

class QCborStreamWriter
{
%TypeHeaderCode
#include <qcborstream.h>
%End

public:
    explicit QCborStreamWriter(QIODevice *device);
    explicit QCborStreamWriter(QByteArray *data);
    ~QCborStreamWriter();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void append(QCborSimpleType st);
    void append(QCborKnownTags tag);
    void append(const QString &str) [void (QStringView str)];
    void append(const QByteArray &ba);
    void append(bool b /Constrained/);
    void append(double d /Constrained/);
%MethodCode
        // Use the smallest type without losing precision.
        
        qfloat16 a0_16 = a0;
        
        if (qIsNaN(a0) || a0_16 == a0)
        {
            sipCpp->append(a0_16);
        }
        else
        {
            float a0_float = a0;
        
            if (a0_float == a0)
                sipCpp->append(a0_float);
            else
                sipCpp->append(a0);
        }
%End

    void append(SIP_PYOBJECT /TypeHint="int"/);
%MethodCode
        #if PY_MAJOR_VERSION < 3
        if (PyLong_Check(a0))
        #endif
        {
            static PyObject *zero = 0;
        
            if (!zero)
                zero = PyLong_FromLong(0);
        
            if (PyObject_RichCompareBool(a0, zero, Py_LT) > 0)
            {
                PyErr_Clear();
                qint64 val = sipLong_AsLongLong(a0);
        
                if (PyErr_Occurred())
                    sipError = sipErrorFail;
                else
                    sipCpp->append(val);
            }
            else
            {
                PyErr_Clear();
                quint64 val = sipLong_AsUnsignedLongLong(a0);
        
                if (PyErr_Occurred())
                    sipError = sipErrorFail;
                else
                    sipCpp->append(val);
            }
        }
        #if PY_MAJOR_VERSION < 3
        else if (PyInt_Check(a0))
        {
            PyErr_Clear();
            long val = PyInt_AsLong(a0);
        
            if (PyErr_Occurred())
                sipError = sipErrorFail;
            else if (val < 0)
                sipCpp->append((qint64)val);
            else
                sipCpp->append((quint64)val);
        }
        #endif
%End

    void appendNull();
    void appendUndefined();
    void startArray();
    void startArray(quint64 count);
    bool endArray();
    void startMap();
    void startMap(quint64 count);
    bool endMap();

private:
    QCborStreamWriter(const QCborStreamWriter &);
};

%End
%If (Qt_5_12_0 -)

class QCborStreamReader
{
%TypeHeaderCode
#include <qcborstream.h>
%End

public:
    enum Type
    {
        UnsignedInteger,
        NegativeInteger,
        ByteString,
        ByteArray,
        TextString,
        String,
        Array,
        Map,
        Tag,
        SimpleType,
        HalfFloat,
        Float16,
        Float,
        Double,
        Invalid,
    };

    enum StringResultCode
    {
        EndOfString,
        Ok,
        Error,
    };

    QCborStreamReader();
    explicit QCborStreamReader(const QByteArray &data);
    explicit QCborStreamReader(QIODevice *device);
    ~QCborStreamReader();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void addData(const QByteArray &data);
    void reparse();
    void clear();
    void reset();
    QCborError lastError();
    qint64 currentOffset() const;
    bool isValid() const;
    int containerDepth() const;
    QCborStreamReader::Type parentContainerType() const;
    bool hasNext() const;
    bool next(int maxRecursion = 10000);
    QCborStreamReader::Type type() const;
    bool isUnsignedInteger() const;
    bool isNegativeInteger() const;
    bool isInteger() const;
    bool isByteArray() const;
    bool isString() const;
    bool isArray() const;
    bool isMap() const;
    bool isTag() const;
    bool isSimpleType() const;
    bool isFloat16() const;
    bool isFloat() const;
    bool isDouble() const;
    bool isInvalid() const;
    bool isSimpleType(QCborSimpleType st) const;
    bool isFalse() const;
    bool isTrue() const;
    bool isBool() const;
    bool isNull() const;
    bool isUndefined() const;
    bool isLengthKnown() const;
    quint64 length() const /__len__/;
    bool isContainer() const;
    bool enterContainer();
    bool leaveContainer();
    SIP_PYTUPLE readString() /TypeHint="Tuple[str, QCborStreamReader.StringResultCode]"/;
%MethodCode
        QCborStreamReader::StringResult<QString> res = sipCpp->readString();
        
        QString *qs = new QString;
        if (res.status != QCborStreamReader::Error)
            *qs = res.data;
        
        sipRes = sipBuildResult(NULL, "NF", qs, sipType_QString, NULL, res.status, sipType_QCborStreamReader_StringResultCode);
%End

    SIP_PYTUPLE readByteArray() /TypeHint="Tuple[QByteArray, QCborStreamReader.StringResultCode]"/;
%MethodCode
        QCborStreamReader::StringResult<QByteArray> res = sipCpp->readByteArray();
        
        QByteArray *qba = new QByteArray;
        if (res.status != QCborStreamReader::Error)
            *qba = res.data;
        
        sipRes = sipBuildResult(NULL, "NF", qba, sipType_QByteArray, NULL, res.status, sipType_QCborStreamReader_StringResultCode);
%End

    bool toBool() const;
    quint64 toUnsignedInteger() const;
    QCborSimpleType toSimpleType() const;
    double toDouble() const;
    qint64 toInteger() const;

private:
    QCborStreamReader(const QCborStreamReader &);
};

%End
