import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtQuick.Scene2D 2.15'

Module {
    dependencies: ["Qt3D.Core 2.0", "Qt3D.Render 2.0"]
    Component {
        name: "Qt3DRender::Quick::QScene2D"
        defaultProperty: "item"
        prototype: "Qt3DCore::QNode"
        exports: ["QtQuick.Scene2D/Scene2D 2.9"]
        exportMetaObjectRevisions: [209]
        Enum {
            name: "RenderPolicy"
            values: {
                "Continuous": 0,
                "SingleShot": 1
            }
        }
        Property { name: "output"; type: "Qt3DRender::QRenderTargetOutput"; isPointer: true }
        Property { name: "renderPolicy"; type: "QScene2D::RenderPolicy" }
        Property { name: "item"; type: "QQuickItem"; isPointer: true }
        Property { name: "mouseEnabled"; type: "bool" }
        Signal {
            name: "outputChanged"
            Parameter { name: "output"; type: "Qt3DRender::QRenderTargetOutput"; isPointer: true }
        }
        Signal {
            name: "renderPolicyChanged"
            Parameter { name: "policy"; type: "QScene2D::RenderPolicy" }
        }
        Signal {
            name: "itemChanged"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "mouseEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setOutput"
            Parameter { name: "output"; type: "Qt3DRender::QRenderTargetOutput"; isPointer: true }
        }
        Method {
            name: "setRenderPolicy"
            Parameter { name: "policy"; type: "QScene2D::RenderPolicy" }
        }
        Method {
            name: "setItem"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "setMouseEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Property {
            name: "entities"
            revision: 209
            type: "Qt3DCore::QEntity"
            isList: true
            isReadonly: true
        }
    }
}
