import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtQuick.Dialogs 1.3'

Module {
    dependencies: [
        "Qt.labs.folderlistmodel 2.1",
        "Qt.labs.settings 1.0",
        "QtGraphicalEffects 1.12",
        "QtQml 2.14",
        "QtQml.Models 2.2",
        "QtQuick 2.9",
        "QtQuick.Controls 1.5",
        "QtQuick.Controls.Styles 1.4",
        "QtQuick.Extras 1.4",
        "QtQuick.Layouts 1.1",
        "QtQuick.Window 2.2"
    ]
    Component {
        name: "QQuickAbstractColorDialog"
        prototype: "QQuickAbstractDialog"
        Property { name: "showAlphaChannel"; type: "bool" }
        Property { name: "color"; type: "QColor" }
        Property { name: "currentColor"; type: "QColor" }
        Property { name: "currentHue"; type: "double"; isReadonly: true }
        Property { name: "currentSaturation"; type: "double"; isReadonly: true }
        Property { name: "currentLightness"; type: "double"; isReadonly: true }
        Property { name: "currentAlpha"; type: "double"; isReadonly: true }
        Signal { name: "selectionAccepted" }
        Method {
            name: "setVisible"
            Parameter { name: "v"; type: "bool" }
        }
        Method {
            name: "setModality"
            Parameter { name: "m"; type: "Qt::WindowModality" }
        }
        Method {
            name: "setTitle"
            Parameter { name: "t"; type: "string" }
        }
        Method {
            name: "setColor"
            Parameter { name: "arg"; type: "QColor" }
        }
        Method {
            name: "setCurrentColor"
            Parameter { name: "currentColor"; type: "QColor" }
        }
        Method {
            name: "setShowAlphaChannel"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        name: "QQuickAbstractDialog"
        prototype: "QObject"
        Enum {
            name: "StandardButton"
            values: {
                "NoButton": 0,
                "Ok": 1024,
                "Save": 2048,
                "SaveAll": 4096,
                "Open": 8192,
                "Yes": 16384,
                "YesToAll": 32768,
                "No": 65536,
                "NoToAll": 131072,
                "Abort": 262144,
                "Retry": 524288,
                "Ignore": 1048576,
                "Close": 2097152,
                "Cancel": 4194304,
                "Discard": 8388608,
                "Help": 16777216,
                "Apply": 33554432,
                "Reset": 67108864,
                "RestoreDefaults": 134217728,
                "NButtons": 134217729
            }
        }
        Enum {
            name: "StandardButtons"
            values: {
                "NoButton": 0,
                "Ok": 1024,
                "Save": 2048,
                "SaveAll": 4096,
                "Open": 8192,
                "Yes": 16384,
                "YesToAll": 32768,
                "No": 65536,
                "NoToAll": 131072,
                "Abort": 262144,
                "Retry": 524288,
                "Ignore": 1048576,
                "Close": 2097152,
                "Cancel": 4194304,
                "Discard": 8388608,
                "Help": 16777216,
                "Apply": 33554432,
                "Reset": 67108864,
                "RestoreDefaults": 134217728,
                "NButtons": 134217729
            }
        }
        Property { name: "visible"; type: "bool" }
        Property { name: "modality"; type: "Qt::WindowModality" }
        Property { name: "title"; type: "string" }
        Property { name: "isWindow"; type: "bool"; isReadonly: true }
        Property { name: "x"; type: "int" }
        Property { name: "y"; type: "int" }
        Property { name: "width"; type: "int" }
        Property { name: "height"; type: "int" }
        Property { name: "__maximumDimension"; type: "int"; isReadonly: true }
        Signal { name: "visibilityChanged" }
        Signal { name: "geometryChanged" }
        Signal { name: "accepted" }
        Signal { name: "rejected" }
        Method { name: "open" }
        Method { name: "close" }
        Method {
            name: "setX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "arg"; type: "int" }
        }
    }
    Component {
        name: "QQuickAbstractFileDialog"
        prototype: "QQuickAbstractDialog"
        Property { name: "selectExisting"; type: "bool" }
        Property { name: "selectMultiple"; type: "bool" }
        Property { name: "selectFolder"; type: "bool" }
        Property { name: "folder"; type: "QUrl" }
        Property { name: "nameFilters"; type: "QStringList" }
        Property { name: "selectedNameFilter"; type: "string" }
        Property { name: "selectedNameFilterExtensions"; type: "QStringList"; isReadonly: true }
        Property { name: "selectedNameFilterIndex"; type: "int" }
        Property { name: "fileUrl"; type: "QUrl"; isReadonly: true }
        Property { name: "fileUrls"; type: "QList<QUrl>"; isReadonly: true }
        Property { name: "sidebarVisible"; type: "bool" }
        Property { name: "defaultSuffix"; type: "string" }
        Property { name: "shortcuts"; type: "QJSValue"; isReadonly: true }
        Property { name: "__shortcuts"; type: "QJSValue"; isReadonly: true }
        Signal { name: "filterSelected" }
        Signal { name: "fileModeChanged" }
        Signal { name: "selectionAccepted" }
        Method {
            name: "setVisible"
            Parameter { name: "v"; type: "bool" }
        }
        Method {
            name: "setTitle"
            Parameter { name: "t"; type: "string" }
        }
        Method {
            name: "setSelectExisting"
            Parameter { name: "s"; type: "bool" }
        }
        Method {
            name: "setSelectMultiple"
            Parameter { name: "s"; type: "bool" }
        }
        Method {
            name: "setSelectFolder"
            Parameter { name: "s"; type: "bool" }
        }
        Method {
            name: "setFolder"
            Parameter { name: "f"; type: "QUrl" }
        }
        Method {
            name: "setNameFilters"
            Parameter { name: "f"; type: "QStringList" }
        }
        Method {
            name: "selectNameFilter"
            Parameter { name: "f"; type: "string" }
        }
        Method {
            name: "setSelectedNameFilterIndex"
            Parameter { name: "idx"; type: "int" }
        }
        Method {
            name: "setSidebarVisible"
            Parameter { name: "s"; type: "bool" }
        }
        Method {
            name: "setDefaultSuffix"
            Parameter { name: "suffix"; type: "string" }
        }
    }
    Component {
        name: "QQuickAbstractFontDialog"
        prototype: "QQuickAbstractDialog"
        Property { name: "scalableFonts"; type: "bool" }
        Property { name: "nonScalableFonts"; type: "bool" }
        Property { name: "monospacedFonts"; type: "bool" }
        Property { name: "proportionalFonts"; type: "bool" }
        Property { name: "font"; type: "QFont" }
        Property { name: "currentFont"; type: "QFont" }
        Signal { name: "selectionAccepted" }
        Method {
            name: "setVisible"
            Parameter { name: "v"; type: "bool" }
        }
        Method {
            name: "setModality"
            Parameter { name: "m"; type: "Qt::WindowModality" }
        }
        Method {
            name: "setTitle"
            Parameter { name: "t"; type: "string" }
        }
        Method {
            name: "setFont"
            Parameter { name: "arg"; type: "QFont" }
        }
        Method {
            name: "setCurrentFont"
            Parameter { name: "arg"; type: "QFont" }
        }
        Method {
            name: "setScalableFonts"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setNonScalableFonts"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setMonospacedFonts"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setProportionalFonts"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        name: "QQuickAbstractMessageDialog"
        prototype: "QQuickAbstractDialog"
        Enum {
            name: "Icon"
            values: {
                "NoIcon": 0,
                "Information": 1,
                "Warning": 2,
                "Critical": 3,
                "Question": 4
            }
        }
        Property { name: "text"; type: "string" }
        Property { name: "informativeText"; type: "string" }
        Property { name: "detailedText"; type: "string" }
        Property { name: "icon"; type: "Icon" }
        Property { name: "standardIconSource"; type: "QUrl"; isReadonly: true }
        Property { name: "standardButtons"; type: "QQuickAbstractDialog::StandardButtons" }
        Property {
            name: "clickedButton"
            type: "QQuickAbstractDialog::StandardButton"
            isReadonly: true
        }
        Signal { name: "buttonClicked" }
        Signal { name: "discard" }
        Signal { name: "help" }
        Signal { name: "yes" }
        Signal { name: "no" }
        Signal { name: "apply" }
        Signal { name: "reset" }
        Method {
            name: "setVisible"
            Parameter { name: "v"; type: "bool" }
        }
        Method {
            name: "setTitle"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setText"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setInformativeText"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setDetailedText"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setIcon"
            Parameter { name: "icon"; type: "Icon" }
        }
        Method {
            name: "setStandardButtons"
            Parameter { name: "buttons"; type: "StandardButtons" }
        }
        Method {
            name: "click"
            Parameter { name: "button"; type: "QQuickAbstractDialog::StandardButton" }
        }
    }
    Component {
        name: "QQuickColorDialog"
        defaultProperty: "contentItem"
        prototype: "QQuickAbstractColorDialog"
        exports: ["QtQuick.Dialogs/AbstractColorDialog 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "contentItem"; type: "QQuickItem"; isPointer: true }
    }
    Component {
        name: "QQuickDialog1"
        defaultProperty: "contentItem"
        prototype: "QQuickAbstractDialog"
        exports: ["QtQuick.Dialogs/AbstractDialog 1.2"]
        exportMetaObjectRevisions: [0]
        Property { name: "title"; type: "string" }
        Property { name: "standardButtons"; type: "QQuickAbstractDialog::StandardButtons" }
        Property {
            name: "clickedButton"
            type: "QQuickAbstractDialog::StandardButton"
            isReadonly: true
        }
        Property { name: "contentItem"; type: "QQuickItem"; isPointer: true }
        Signal { name: "buttonClicked" }
        Signal { name: "discard" }
        Signal { name: "help" }
        Signal { name: "yes" }
        Signal { name: "no" }
        Signal { name: "apply" }
        Signal { name: "reset" }
        Method {
            name: "setTitle"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setStandardButtons"
            Parameter { name: "buttons"; type: "StandardButtons" }
        }
        Method {
            name: "click"
            Parameter { name: "button"; type: "QQuickAbstractDialog::StandardButton" }
        }
        Method { name: "__standardButtonsLeftModel"; type: "QJSValue" }
        Method { name: "__standardButtonsRightModel"; type: "QJSValue" }
    }
    Component {
        name: "QQuickFileDialog"
        defaultProperty: "contentItem"
        prototype: "QQuickAbstractFileDialog"
        exports: ["QtQuick.Dialogs/AbstractFileDialog 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "contentItem"; type: "QQuickItem"; isPointer: true }
        Method { name: "clearSelection" }
        Method {
            name: "addSelection"
            type: "bool"
            Parameter { name: "path"; type: "QUrl" }
        }
    }
    Component {
        name: "QQuickFontDialog"
        defaultProperty: "contentItem"
        prototype: "QQuickAbstractFontDialog"
        exports: ["QtQuick.Dialogs/AbstractFontDialog 1.1"]
        exportMetaObjectRevisions: [0]
        Property { name: "contentItem"; type: "QQuickItem"; isPointer: true }
    }
    Component {
        name: "QQuickMessageDialog"
        defaultProperty: "contentItem"
        prototype: "QQuickAbstractMessageDialog"
        exports: ["QtQuick.Dialogs/AbstractMessageDialog 1.1"]
        exportMetaObjectRevisions: [0]
        Property { name: "contentItem"; type: "QQuickItem"; isPointer: true }
    }
    Component {
        name: "QQuickStandardButton"
        exports: ["QtQuick.Dialogs/StandardButton 1.1"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickStandardIcon"
        exports: ["QtQuick.Dialogs/StandardIcon 1.1"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        prototype: "QQuickColorDialog"
        name: "QtQuick.Dialogs/ColorDialog 1.0"
        exports: ["QtQuick.Dialogs/ColorDialog 1.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentItem"
        Property { name: "__valueSet"; type: "bool" }
        Method { name: "__setControlsFromColor"; type: "QVariant" }
    }
    Component {
        prototype: "QQuickDialog1"
        name: "QtQuick.Dialogs/Dialog 1.2"
        exports: ["QtQuick.Dialogs/Dialog 1.2"]
        exportMetaObjectRevisions: [2]
        isComposite: true
        defaultProperty: "data"
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Signal {
            name: "actionChosen"
            Parameter { name: "action"; type: "QVariant" }
        }
        Method { name: "setupButtons"; type: "QVariant" }
    }
    Component {
        prototype: "QQuickDialog1"
        name: "QtQuick.Dialogs/Dialog 1.3"
        exports: ["QtQuick.Dialogs/Dialog 1.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "data"
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Signal {
            name: "actionChosen"
            Parameter { name: "action"; type: "QVariant" }
        }
        Method { name: "setupButtons"; type: "QVariant" }
    }
    Component {
        prototype: "QQuickFileDialog"
        name: "QtQuick.Dialogs/FileDialog 1.0"
        exports: ["QtQuick.Dialogs/FileDialog 1.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentItem"
        Property { name: "modelComponent"; type: "QQmlComponent"; isPointer: true }
        Property { name: "settings"; type: "QQmlSettings"; isPointer: true }
        Property { name: "showFocusHighlight"; type: "bool" }
        Property { name: "palette"; type: "QQuickSystemPalette"; isPointer: true }
        Property { name: "favoriteFolders"; type: "QVariant" }
        Property { name: "dirUpAction"; type: "QQuickAction1"; isPointer: true }
        Method {
            name: "dirDown"
            type: "QVariant"
            Parameter { name: "path"; type: "QVariant" }
        }
        Method { name: "dirUp"; type: "QVariant" }
        Method { name: "acceptSelection"; type: "QVariant" }
    }
    Component {
        prototype: "QQuickFontDialog"
        name: "QtQuick.Dialogs/FontDialog 1.1"
        exports: ["QtQuick.Dialogs/FontDialog 1.1"]
        exportMetaObjectRevisions: [1]
        isComposite: true
        defaultProperty: "contentItem"
        Property { name: "font"; type: "QFont" }
        Property { name: "currentFont"; type: "QFont" }
    }
    Component {
        prototype: "QQuickMessageDialog"
        name: "QtQuick.Dialogs/MessageDialog 1.1"
        exports: ["QtQuick.Dialogs/MessageDialog 1.1"]
        exportMetaObjectRevisions: [1]
        isComposite: true
        defaultProperty: "contentItem"
        Method { name: "calculateImplicitWidth"; type: "QVariant" }
    }
}
