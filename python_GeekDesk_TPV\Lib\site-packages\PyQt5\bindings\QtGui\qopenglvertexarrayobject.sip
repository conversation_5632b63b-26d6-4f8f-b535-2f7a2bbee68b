// qopenglvertexarrayobject.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)
%If (PyQt_OpenGL)

class QOpenGLVertexArrayObject : public QObject
{
%TypeHeaderCode
#include <qopenglvertexarrayobject.h>
%End

public:
    explicit QOpenGLVertexArrayObject(QObject *parent /TransferThis/ = 0);
    virtual ~QOpenGLVertexArrayObject();
    bool create();
    void destroy();
    bool isCreated() const;
    GLuint objectId() const;
    void bind();
    void release();

    class Binder
    {
%TypeHeaderCode
#include <qopenglvertexarrayobject.h>
%End

    public:
        Binder(QOpenGLVertexArrayObject *v);
        ~Binder();
        void release();
        void rebind();
        SIP_PYOBJECT __enter__();
%MethodCode
            // Just return a reference to self.
            sipRes = sipSelf;
            Py_INCREF(sipRes);
%End

        void __exit__(SIP_PYOBJECT type, SIP_PYOBJECT value, SIP_PYOBJECT traceback);
%MethodCode
            sipCpp->release();
%End

    private:
        Binder(const QOpenGLVertexArrayObject::Binder &);
    };
};

%End
%End
