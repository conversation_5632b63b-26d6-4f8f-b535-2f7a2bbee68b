// qvariantanimation.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QVariantAnimation : public QAbstractAnimation
{
%TypeHeaderCode
#include <qvariantanimation.h>
%End

    typedef QVector<QPair<qreal, QVariant>> KeyValues;

public:
    QVariantAnimation(QObject *parent /TransferThis/ = 0);
    virtual ~QVariantAnimation();
    QVariant startValue() const;
    void setStartValue(const QVariant &value);
    QVariant endValue() const;
    void setEndValue(const QVariant &value);
    QVariant keyValueAt(qreal step) const;
    void setKeyValueAt(qreal step, const QVariant &value);
    QVariantAnimation::KeyValues keyValues() const;
    void setKeyValues(const QVariantAnimation::KeyValues &values);
    QVariant currentValue() const;
    virtual int duration() const;
    void setDuration(int msecs);
    QEasingCurve easingCurve() const;
    void setEasingCurve(const QEasingCurve &easing);

signals:
    void valueChanged(const QVariant &value);

protected:
    virtual bool event(QEvent *event);
    virtual void updateCurrentTime(int);
    virtual void updateState(QAbstractAnimation::State newState, QAbstractAnimation::State oldState);
    virtual void updateCurrentValue(const QVariant &value);
    virtual QVariant interpolated(const QVariant &from, const QVariant &to, qreal progress) const;
};
