#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菜單項模型
"""

from typing import Dict, Any, Optional, List
from pathlib import Path

from .base import BaseModel
from constants.enums import MenuType, IconType


class MenuItem(BaseModel):
    """菜單項模型"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 基本屬性
        self.name = kwargs.get('name', '')
        self.description = kwargs.get('description', '')
        self.menu_type = MenuType(kwargs.get('menu_type', MenuType.APPLICATION))
        
        # 路徑和參數
        self.path = kwargs.get('path', '')
        self.arguments = kwargs.get('arguments', '')
        self.working_directory = kwargs.get('working_directory', '')
        
        # 圖標
        self.icon_type = IconType(kwargs.get('icon_type', IconType.SYSTEM))
        self.icon_path = kwargs.get('icon_path', '')
        self.icon_name = kwargs.get('icon_name', '')
        
        # 熱鍵
        self.hotkey = kwargs.get('hotkey', '')
        
        # 顯示設置
        self.visible = kwargs.get('visible', True)
        self.sort_order = kwargs.get('sort_order', 0)
        
        # 分組
        self.group = kwargs.get('group', 'default')
        
        # 標籤
        self.tags = kwargs.get('tags', [])
        
        # 使用統計
        self.use_count = kwargs.get('use_count', 0)
        self.last_used = kwargs.get('last_used', None)
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        base_dict = super().to_dict()
        base_dict.update({
            'name': self.name,
            'description': self.description,
            'menu_type': self.menu_type.value,
            'path': self.path,
            'arguments': self.arguments,
            'working_directory': self.working_directory,
            'icon_type': self.icon_type.value,
            'icon_path': self.icon_path,
            'icon_name': self.icon_name,
            'hotkey': self.hotkey,
            'visible': self.visible,
            'sort_order': self.sort_order,
            'group': self.group,
            'tags': self.tags,
            'use_count': self.use_count,
            'last_used': self.last_used
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MenuItem':
        """從字典創建實例"""
        return cls(**data)
    
    def execute(self) -> bool:
        """執行菜單項"""
        try:
            import subprocess
            import os
            from datetime import datetime
            
            # 更新使用統計
            self.use_count += 1
            self.last_used = datetime.now()
            
            if self.menu_type == MenuType.APPLICATION:
                # 啟動應用程序
                if self.working_directory and Path(self.working_directory).exists():
                    cwd = self.working_directory
                else:
                    cwd = None
                
                if self.arguments:
                    subprocess.Popen(
                        f'"{self.path}" {self.arguments}',
                        cwd=cwd,
                        shell=True
                    )
                else:
                    subprocess.Popen(self.path, cwd=cwd, shell=True)
                
            elif self.menu_type == MenuType.FILE:
                # 打開文件
                os.startfile(self.path)
                
            elif self.menu_type == MenuType.FOLDER:
                # 打開文件夾
                os.startfile(self.path)
                
            elif self.menu_type == MenuType.URL:
                # 打開URL
                import webbrowser
                webbrowser.open(self.path)
                
            elif self.menu_type == MenuType.COMMAND:
                # 執行命令
                subprocess.Popen(self.path, shell=True)
            
            return True
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"執行菜單項失敗: {e}")
            return False
    
    def is_valid(self) -> bool:
        """檢查菜單項是否有效"""
        if not self.name or not self.path:
            return False
        
        if self.menu_type in [MenuType.APPLICATION, MenuType.FILE]:
            return Path(self.path).exists()
        elif self.menu_type == MenuType.FOLDER:
            return Path(self.path).is_dir()
        elif self.menu_type == MenuType.URL:
            return self.path.startswith(('http://', 'https://'))
        elif self.menu_type == MenuType.COMMAND:
            return bool(self.path.strip())
        
        return True
    
    def get_icon_path(self) -> Optional[str]:
        """獲取圖標路徑"""
        if self.icon_type == IconType.CUSTOM and self.icon_path:
            if Path(self.icon_path).exists():
                return self.icon_path
        
        # 返回系統圖標或默認圖標
        return None
    
    def add_tag(self, tag: str):
        """添加標籤"""
        if tag and tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str):
        """移除標籤"""
        if tag in self.tags:
            self.tags.remove(tag)
    
    def has_tag(self, tag: str) -> bool:
        """檢查是否有指定標籤"""
        return tag in self.tags
    
    def matches_search(self, query: str) -> bool:
        """檢查是否匹配搜索查詢"""
        query = query.lower()
        
        # 檢查名稱
        if query in self.name.lower():
            return True
        
        # 檢查描述
        if query in self.description.lower():
            return True
        
        # 檢查標籤
        for tag in self.tags:
            if query in tag.lower():
                return True
        
        # 檢查路徑
        if query in self.path.lower():
            return True
        
        return False
