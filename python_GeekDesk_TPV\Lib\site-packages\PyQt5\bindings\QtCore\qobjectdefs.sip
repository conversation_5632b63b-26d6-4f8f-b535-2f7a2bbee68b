// qobjectdefs.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


struct QMetaObject
{
%TypeHeaderCode
#include <qobjectdefs.h>
%End

%TypeCode
// Raise an exception when QMetaObject::invokeMethod() returns false.
static void qtcore_invokeMethod_exception()
{
    PyErr_SetString(PyExc_RuntimeError, "QMetaObject.invokeMethod() call failed");
}
%End

    const char *className() const;
    const QMetaObject *superClass() const;
    QMetaProperty userProperty() const;
    int methodOffset() const;
    int enumeratorOffset() const;
    int propertyOffset() const;
    int classInfoOffset() const;
    int methodCount() const;
    int enumeratorCount() const;
    int propertyCount() const;
    int classInfoCount() const;
    int indexOfMethod(const char *method) const;
    int indexOfSignal(const char *signal) const;
    int indexOfSlot(const char *slot) const;
    int indexOfEnumerator(const char *name) const;
    int indexOfProperty(const char *name) const;
    int indexOfClassInfo(const char *name) const;
    QMetaMethod method(int index) const;
    QMetaEnum enumerator(int index) const;
    QMetaProperty property(int index) const;
    QMetaClassInfo classInfo(int index) const;
    static bool checkConnectArgs(const char *signal, const char *method);
    static void connectSlotsByName(QObject *o /GetWrapper/);
%MethodCode
        qpycore_qmetaobject_connectslotsbyname(a0, a0Wrapper);
        
        // Make sure there is no (benign) Python exception.
        PyErr_Clear();
%End

    static QByteArray normalizedSignature(const char *method);
    static QByteArray normalizedType(const char *type);
    static SIP_PYOBJECT invokeMethod(QObject *obj, const char *member, Qt::ConnectionType type, QGenericReturnArgument ret /GetWrapper/, QGenericArgument value0 = QGenericArgument(0u, 0u), QGenericArgument value1 = QGenericArgument(0u, 0u), QGenericArgument value2 = QGenericArgument(0u, 0u), QGenericArgument value3 = QGenericArgument(0u, 0u), QGenericArgument value4 = QGenericArgument(0u, 0u), QGenericArgument value5 = QGenericArgument(0u, 0u), QGenericArgument value6 = QGenericArgument(0u, 0u), QGenericArgument value7 = QGenericArgument(0u, 0u), QGenericArgument value8 = QGenericArgument(0u, 0u), QGenericArgument value9 = QGenericArgument(0u, 0u));
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = QMetaObject::invokeMethod(a0,a1,a2,*a3,*a4,*a5,*a6,*a7,*a8,*a9,*a10,*a11,*a12,*a13);
        Py_END_ALLOW_THREADS
        
        if (ok)
            sipRes = qpycore_ReturnValue(a3Wrapper);
        else
            qtcore_invokeMethod_exception();
%End

    static SIP_PYOBJECT invokeMethod(QObject *obj, const char *member, QGenericReturnArgument ret /GetWrapper/, QGenericArgument value0 = QGenericArgument(0u, 0u), QGenericArgument value1 = QGenericArgument(0u, 0u), QGenericArgument value2 = QGenericArgument(0u, 0u), QGenericArgument value3 = QGenericArgument(0u, 0u), QGenericArgument value4 = QGenericArgument(0u, 0u), QGenericArgument value5 = QGenericArgument(0u, 0u), QGenericArgument value6 = QGenericArgument(0u, 0u), QGenericArgument value7 = QGenericArgument(0u, 0u), QGenericArgument value8 = QGenericArgument(0u, 0u), QGenericArgument value9 = QGenericArgument(0u, 0u));
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = QMetaObject::invokeMethod(a0,a1,*a2,*a3,*a4,*a5,*a6,*a7,*a8,*a9,*a10,*a11,*a12);
        Py_END_ALLOW_THREADS
        
        if (ok)
            sipRes = qpycore_ReturnValue(a2Wrapper);
        else
            qtcore_invokeMethod_exception();
%End

    static SIP_PYOBJECT invokeMethod(QObject *obj, const char *member, Qt::ConnectionType type, QGenericArgument value0 = QGenericArgument(0u, 0u), QGenericArgument value1 = QGenericArgument(0u, 0u), QGenericArgument value2 = QGenericArgument(0u, 0u), QGenericArgument value3 = QGenericArgument(0u, 0u), QGenericArgument value4 = QGenericArgument(0u, 0u), QGenericArgument value5 = QGenericArgument(0u, 0u), QGenericArgument value6 = QGenericArgument(0u, 0u), QGenericArgument value7 = QGenericArgument(0u, 0u), QGenericArgument value8 = QGenericArgument(0u, 0u), QGenericArgument value9 = QGenericArgument(0u, 0u));
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = QMetaObject::invokeMethod(a0,a1,a2,*a3,*a4,*a5,*a6,*a7,*a8,*a9,*a10,*a11,*a12);
        Py_END_ALLOW_THREADS
        
        if (ok)
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
        else
            qtcore_invokeMethod_exception();
%End

    static SIP_PYOBJECT invokeMethod(QObject *obj, const char *member, QGenericArgument value0 = QGenericArgument(0u, 0u), QGenericArgument value1 = QGenericArgument(0u, 0u), QGenericArgument value2 = QGenericArgument(0u, 0u), QGenericArgument value3 = QGenericArgument(0u, 0u), QGenericArgument value4 = QGenericArgument(0u, 0u), QGenericArgument value5 = QGenericArgument(0u, 0u), QGenericArgument value6 = QGenericArgument(0u, 0u), QGenericArgument value7 = QGenericArgument(0u, 0u), QGenericArgument value8 = QGenericArgument(0u, 0u), QGenericArgument value9 = QGenericArgument(0u, 0u));
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = QMetaObject::invokeMethod(a0,a1,*a2,*a3,*a4,*a5,*a6,*a7,*a8,*a9,*a10,*a11);
        Py_END_ALLOW_THREADS
        
        if (ok)
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
        else
            qtcore_invokeMethod_exception();
%End

    int constructorCount() const;
    int indexOfConstructor(const char *constructor) const;
    QMetaMethod constructor(int index) const;
    static bool checkConnectArgs(const QMetaMethod &signal, const QMetaMethod &method);
%If (Qt_5_7_0 -)
    bool inherits(const QMetaObject *metaObject) const;
%End

    class Connection
    {
%TypeHeaderCode
#include <qobjectdefs.h>
%End

    public:
        Connection();
        Connection(const QMetaObject::Connection &other);
        ~Connection();
    };
};

// The support for Q_ARG(), Q_RETURN_ARG() and supporting classes.
class QGenericArgument /NoDefaultCtors/
{
%TypeHeaderCode
#include <qobjectdefs.h>
%End

public:
    ~QGenericArgument();
};


SIP_PYOBJECT Q_ARG(SIP_PYOBJECT type, SIP_PYOBJECT data) /TypeHint="QGenericArgument"/;
%MethodCode
    sipRes = qpycore_ArgumentFactory(a0, a1);
%End


class QGenericReturnArgument /NoDefaultCtors/
{
%TypeHeaderCode
#include <qobjectdefs.h>
%End

public:
    ~QGenericReturnArgument();
};


SIP_PYOBJECT Q_RETURN_ARG(SIP_PYOBJECT type) /TypeHint="QGenericReturnArgument"/;
%MethodCode
    sipRes = qpycore_ReturnFactory(a0);
%End
