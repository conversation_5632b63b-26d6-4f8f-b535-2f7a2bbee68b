#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器測試
"""

import pytest
import tempfile
import json
from pathlib import Path

from utils.config_manager import ConfigManager


class TestConfigManager:
    """配置管理器測試類"""
    
    def setup_method(self):
        """測試前設置"""
        # 創建臨時目錄
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config_file = self.temp_dir / "config.json"
        self.user_config_file = self.temp_dir / "user_config.json"
        
        # 創建測試配置
        self.test_config = {
            "app": {
                "name": "test_app",
                "version": "1.0.0"
            },
            "ui": {
                "theme": "light",
                "opacity": 0.8
            }
        }
        
        # 寫入測試配置文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_config, f)
    
    def teardown_method(self):
        """測試後清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_load_config(self):
        """測試加載配置"""
        # 模擬配置文件路徑
        import constants.constants as const
        original_config_file = const.CONFIG_FILE
        original_user_config_file = const.USER_CONFIG_FILE
        
        const.CONFIG_FILE = self.config_file
        const.USER_CONFIG_FILE = self.user_config_file
        
        try:
            config_manager = ConfigManager()
            
            # 測試讀取配置
            app_name = config_manager.get_setting("app.name")
            assert app_name == "test_app"
            
            theme = config_manager.get_setting("ui.theme")
            assert theme == "light"
            
        finally:
            # 恢復原始路徑
            const.CONFIG_FILE = original_config_file
            const.USER_CONFIG_FILE = original_user_config_file
    
    def test_set_setting(self):
        """測試設置配置"""
        import constants.constants as const
        original_config_file = const.CONFIG_FILE
        original_user_config_file = const.USER_CONFIG_FILE
        
        const.CONFIG_FILE = self.config_file
        const.USER_CONFIG_FILE = self.user_config_file
        
        try:
            config_manager = ConfigManager()
            
            # 設置新值
            config_manager.set_setting("ui.opacity", 0.9)
            
            # 驗證設置
            opacity = config_manager.get_setting("ui.opacity")
            assert opacity == 0.9
            
            # 設置嵌套值
            config_manager.set_setting("new.nested.value", "test")
            value = config_manager.get_setting("new.nested.value")
            assert value == "test"
            
        finally:
            const.CONFIG_FILE = original_config_file
            const.USER_CONFIG_FILE = original_user_config_file
    
    def test_get_default_value(self):
        """測試獲取默認值"""
        import constants.constants as const
        original_config_file = const.CONFIG_FILE
        original_user_config_file = const.USER_CONFIG_FILE
        
        const.CONFIG_FILE = self.config_file
        const.USER_CONFIG_FILE = self.user_config_file
        
        try:
            config_manager = ConfigManager()
            
            # 測試不存在的鍵
            value = config_manager.get_setting("non.existent.key", "default_value")
            assert value == "default_value"
            
        finally:
            const.CONFIG_FILE = original_config_file
            const.USER_CONFIG_FILE = original_user_config_file
    
    def test_reset_setting(self):
        """測試重置設置"""
        import constants.constants as const
        original_config_file = const.CONFIG_FILE
        original_user_config_file = const.USER_CONFIG_FILE
        
        const.CONFIG_FILE = self.config_file
        const.USER_CONFIG_FILE = self.user_config_file
        
        try:
            config_manager = ConfigManager()
            
            # 設置用戶配置
            config_manager.set_setting("ui.opacity", 0.5)
            assert config_manager.get_setting("ui.opacity") == 0.5
            
            # 重置設置
            config_manager.reset_setting("ui.opacity")
            
            # 應該回到默認值
            opacity = config_manager.get_setting("ui.opacity")
            assert opacity == 0.8  # 默認配置中的值
            
        finally:
            const.CONFIG_FILE = original_config_file
            const.USER_CONFIG_FILE = original_user_config_file
    
    def test_export_import_settings(self):
        """測試導出導入設置"""
        import constants.constants as const
        original_config_file = const.CONFIG_FILE
        original_user_config_file = const.USER_CONFIG_FILE
        
        const.CONFIG_FILE = self.config_file
        const.USER_CONFIG_FILE = self.user_config_file
        
        try:
            config_manager = ConfigManager()
            
            # 設置一些值
            config_manager.set_setting("test.value1", "exported")
            config_manager.set_setting("test.value2", 123)
            
            # 導出設置
            export_file = self.temp_dir / "exported_settings.json"
            success = config_manager.export_settings(export_file)
            assert success
            assert export_file.exists()
            
            # 修改設置
            config_manager.set_setting("test.value1", "modified")
            
            # 導入設置
            success = config_manager.import_settings(export_file)
            assert success
            
            # 驗證導入的值
            value1 = config_manager.get_setting("test.value1")
            value2 = config_manager.get_setting("test.value2")
            assert value1 == "exported"
            assert value2 == 123
            
        finally:
            const.CONFIG_FILE = original_config_file
            const.USER_CONFIG_FILE = original_user_config_file


if __name__ == "__main__":
    pytest.main([__file__])
