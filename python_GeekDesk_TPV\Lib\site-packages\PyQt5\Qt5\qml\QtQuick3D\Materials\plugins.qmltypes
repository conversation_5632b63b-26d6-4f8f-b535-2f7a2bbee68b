import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtQuick3D.Materials 1.15'

Module {
    dependencies: [
        "QtQuick 2.15",
        "QtQuick.Window 2.1",
        "QtQuick3D 1.15",
        "QtQuick3D.Effects 1.15"
    ]
    Component {
        name: "QQuick3DCustomMaterial"
        defaultProperty: "data"
        prototype: "QQuick3DMaterial"
        exports: ["QtQuick3D.Materials/CustomMaterial 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "hasTransparency"; type: "bool" }
        Property { name: "hasRefraction"; type: "bool" }
        Property { name: "alwaysDirty"; type: "bool" }
        Property { name: "shaderInfo"; type: "QQuick3DShaderUtilsShaderInfo"; isPointer: true }
        Property { name: "passes"; type: "QQuick3DShaderUtilsRenderPass"; isList: true; isReadonly: true }
        Signal {
            name: "hasTransparencyChanged"
            Parameter { name: "hasTransparency"; type: "bool" }
        }
        Signal {
            name: "hasRefractionChanged"
            Parameter { name: "hasRefraction"; type: "bool" }
        }
        Signal {
            name: "alwaysDirtyChanged"
            Parameter { name: "alwaysDirty"; type: "bool" }
        }
        Method {
            name: "setHasTransparency"
            Parameter { name: "hasTransparency"; type: "bool" }
        }
        Method {
            name: "setHasRefraction"
            Parameter { name: "hasRefraction"; type: "bool" }
        }
        Method {
            name: "setShaderInfo"
            Parameter { name: "shaderInfo"; type: "QQuick3DShaderUtilsShaderInfo"; isPointer: true }
        }
        Method {
            name: "setAlwaysDirty"
            Parameter { name: "alwaysDirty"; type: "bool" }
        }
    }
}
