# 圖標資源說明

此目錄包含 python_GeekDesk_TPV 使用的圖標文件。

## 圖標文件列表

### 應用程序圖標
- `logo.ico` - 主程序圖標（建議 256x256 像素）
- `logo.png` - PNG 格式的主程序圖標

### 功能圖標
- `show.png` - 顯示主面板圖標
- `todo.png` - 待辦事項圖標
- `settings.png` - 設置圖標
- `about.png` - 關於圖標
- `quit.png` - 退出圖標
- `search.png` - 搜索圖標

### 文件類型圖標
- `file.png` - 默認文件圖標
- `folder.png` - 文件夾圖標
- `application.png` - 應用程序圖標
- `text.png` - 文本文件圖標
- `image.png` - 圖片文件圖標
- `audio.png` - 音頻文件圖標
- `video.png` - 視頻文件圖標
- `archive.png` - 壓縮文件圖標
- `executable.png` - 可執行文件圖標

### 狀態圖標
- `completed.png` - 已完成圖標
- `pending.png` - 待處理圖標
- `overdue.png` - 過期圖標
- `priority_high.png` - 高優先級圖標
- `priority_low.png` - 低優先級圖標

## 圖標規格

- **格式**: PNG 或 ICO
- **大小**: 16x16, 24x24, 32x32, 48x48 像素
- **背景**: 透明背景
- **風格**: 扁平化設計，與系統風格保持一致

## 圖標來源

- 系統圖標：使用 Windows 系統內置圖標
- 自定義圖標：可以從以下來源獲取
  - [Feather Icons](https://feathericons.com/)
  - [Material Design Icons](https://materialdesignicons.com/)
  - [Font Awesome](https://fontawesome.com/)
  - [阿里巴巴矢量圖標庫](https://www.iconfont.cn/)

## 添加新圖標

1. 將圖標文件放入此目錄
2. 確保文件名符合命名規範
3. 在代碼中使用 `ICONS_DIR / "icon_name.png"` 引用
4. 更新此 README 文件

## 注意事項

- 圖標文件應該保持較小的文件大小
- 使用一致的視覺風格
- 確保在不同 DPI 設置下都能清晰顯示
- 遵循版權規定，使用合法的圖標資源
