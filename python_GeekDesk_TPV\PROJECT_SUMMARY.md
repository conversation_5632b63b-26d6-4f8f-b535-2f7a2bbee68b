# python_GeekDesk_TPV 專案文件總結

## 專案概述

python_GeekDesk_TPV 是一個基於 Python 和 PyQt5 的桌面效率工具，提供快速啟動、文件搜索、全局熱鍵、待辦提醒等功能。

## 完整文件列表

### 根目錄文件
```
python_GeekDesk_TPV/
├── main.py                    # 主程序入口文件
├── requirements.txt           # Python 依賴包列表
├── README.md                 # 專案說明文檔
├── setup.py                  # 安裝配置文件
├── build.py                  # 打包構建腳本
├── run_tests.py              # 測試運行腳本
├── start.bat                 # Windows 啟動腳本
├── LICENSE                   # MIT 許可證
├── .gitignore               # Git 忽略文件配置
└── PROJECT_SUMMARY.md        # 專案文件總結（本文件）
```

### 常量定義模塊 (constants/)
```
constants/
├── __init__.py              # 包初始化文件
├── constants.py             # 應用程序常量定義
└── enums.py                # 枚舉類型定義
```

### 核心模塊 (core/)
```
core/
├── __init__.py              # 包初始化文件
└── app.py                  # 主應用程序類
```

### 數據模型 (models/)
```
models/
├── __init__.py              # 包初始化文件
├── base.py                 # 基礎模型類
├── menu_item.py            # 菜單項模型
└── todo_item.py            # 待辦事項模型
```

### 工具模塊 (utils/)
```
utils/
├── __init__.py              # 包初始化文件
├── logger.py               # 日誌管理工具
├── config_manager.py       # 配置管理器
├── hotkey_manager.py       # 全局熱鍵管理器
├── tray_manager.py         # 系統托盤管理器
└── search_manager.py       # 搜索管理器
```

### 視圖層 (views/)
```
views/
├── __init__.py              # 包初始化文件
├── main_window.py          # 主窗口界面
├── settings_window.py      # 設置窗口界面
└── todo_window.py          # 待辦事項窗口界面
```

### 資源文件 (resources/)
```
resources/
├── __init__.py              # 包初始化文件
├── icons/                  # 圖標文件目錄
│   └── README.md           # 圖標使用說明
└── images/                 # 圖片文件目錄
```

### 配置文件 (config/)
```
config/
└── default_config.json     # 默認配置文件
```

### 文檔 (docs/)
```
docs/
└── DEVELOPMENT.md          # 開發文檔
```

### 測試文件 (tests/)
```
tests/
├── __init__.py              # 包初始化文件
├── test_config_manager.py  # 配置管理器測試
└── test_models.py          # 模型測試
```

### 自動創建的目錄
```
logs/                       # 日誌文件目錄（運行時創建）
data/                       # 數據文件目錄（運行時創建）
temp/                       # 臨時文件目錄（運行時創建）
backup/                     # 備份文件目錄（運行時創建）
```

## 核心功能模塊

### 1. 主程序 (main.py)
- 應用程序入口點
- 單實例檢查
- 系統要求驗證
- 異常處理和日誌記錄

### 2. 應用程序核心 (core/app.py)
- 主應用程序邏輯
- 窗口管理
- 系統托盤集成
- 熱鍵註冊
- 信號處理

### 3. 配置管理 (utils/config_manager.py)
- 配置文件讀寫
- 嵌套配置支持
- 配置導入導出
- 配置備份恢復

### 4. 熱鍵管理 (utils/hotkey_manager.py)
- 全局熱鍵註冊
- 熱鍵衝突檢測
- 動態熱鍵更新
- 熱鍵可用性檢查

### 5. 搜索功能 (utils/search_manager.py)
- 文件名搜索
- Everything 集成
- 文件內容搜索
- 異步搜索處理

### 6. 系統托盤 (utils/tray_manager.py)
- 托盤圖標管理
- 右鍵菜單
- 通知消息
- 托盤事件處理

### 7. 主界面 (views/main_window.py)
- 搜索界面
- 結果顯示
- 鼠標跟隨
- 動畫效果

### 8. 設置界面 (views/settings_window.py)
- 分頁設置界面
- 實時配置更新
- 設置驗證
- 重置功能

### 9. 待辦管理 (views/todo_window.py)
- 待辦事項管理
- 提醒功能
- 重複任務
- 優先級管理

## 主要特性

### ✨ 核心功能
- 🚀 **快速啟動**: 自定義菜單和圖標，快速啟動常用程序
- 🔍 **文件搜索**: 強大的文件和內容搜索功能
- ⌨️ **全局熱鍵**: 自定義快捷鍵，隨時呼出面板
- 🎨 **界面美化**: 支持自定義背景、透明度和圓角
- 📝 **待辦提醒**: 定時提醒功能，永不遺忘重要事項
- 🖱️ **鼠標跟隨**: 面板自動跟隨鼠標位置顯示
- 🔧 **高度自定義**: 豐富的配置選項，打造專屬桌面

### 🔥 高級功能
- **Everything 集成**: 快速搜索全盤文件
- **毛玻璃效果**: 背景圖片毛玻璃效果
- **自定義圖標**: 80多個系統圖標可供選擇
- **定時提醒**: 永不忘記重要事項
- **多種搜索**: 文件名、內容、Everything 搜索
- **熱鍵管理**: 全局熱鍵，一鍵呼出

## 技術架構

### 開發語言和框架
- **Python 3.8+**: 主要開發語言
- **PyQt5**: GUI 框架
- **keyboard**: 全局熱鍵支持
- **psutil**: 系統信息獲取
- **colorlog**: 彩色日誌輸出

### 架構設計
- **分層架構**: 清晰的模塊分離
- **MVC 模式**: 模型-視圖-控制器分離
- **信號槽機制**: 組件間通信
- **配置驅動**: 靈活的配置管理
- **異步處理**: 非阻塞搜索和操作

### 設計模式
- **單例模式**: 應用程序實例管理
- **觀察者模式**: 事件處理和通知
- **工廠模式**: 對象創建和管理
- **策略模式**: 不同搜索策略

## 安裝和使用

### 環境要求
- Windows 10/11
- Python 3.8+
- 8GB+ RAM（推薦）

### 快速開始
1. 下載專案文件
2. 運行 `start.bat` 或手動安裝依賴
3. 執行 `python main.py` 啟動程序
4. 使用 `Ctrl+Space` 呼出主面板

### 開發和測試
```bash
# 運行測試
python run_tests.py

# 運行覆蓋率測試
python run_tests.py --coverage

# 運行代碼檢查
python run_tests.py --lint

# 構建可執行文件
python build.py
```

## 文件用途說明

### 核心文件
- `main.py`: 程序入口，處理啟動邏輯
- `requirements.txt`: 定義所有 Python 依賴
- `README.md`: 用戶使用說明和功能介紹

### 配置和構建
- `setup.py`: Python 包安裝配置
- `build.py`: 自動化構建腳本
- `start.bat`: Windows 一鍵啟動腳本

### 開發和測試
- `run_tests.py`: 測試運行器
- `tests/`: 單元測試文件
- `docs/DEVELOPMENT.md`: 開發者文檔

### 資源和配置
- `config/default_config.json`: 默認配置模板
- `resources/`: 圖標和圖片資源
- `.gitignore`: Git 版本控制忽略規則

## 下一步開發計劃

1. **圖標資源**: 添加完整的圖標文件
2. **數據持久化**: 實現待辦事項和菜單的數據庫存儲
3. **插件系統**: 支持第三方插件擴展
4. **主題系統**: 更多界面主題選擇
5. **雲同步**: 配置和數據雲端同步
6. **多語言**: 國際化支持

## 貢獻指南

歡迎提交 Issue 和 Pull Request！請參考 `docs/DEVELOPMENT.md` 了解開發規範。

---

**專案狀態**: ✅ 完整可用  
**最後更新**: 2024-01-XX  
**版本**: v1.0.0
