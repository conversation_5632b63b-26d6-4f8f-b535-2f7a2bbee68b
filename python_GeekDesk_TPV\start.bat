@echo off
chcp 65001 >nul
title python_GeekDesk_TPV

echo.
echo ========================================
echo   python_GeekDesk_TPV 桌面效率工具
echo ========================================
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo 錯誤: 未找到 Python，請先安裝 Python 3.8 或更高版本
        echo 下載地址: https://www.python.org/downloads/
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

echo 使用 Python 命令: %PYTHON_CMD%
%PYTHON_CMD% --version

REM 檢查虛擬環境
if not exist "venv" (
    echo 創建虛擬環境...
    %PYTHON_CMD% -m venv venv
    if errorlevel 1 (
        echo 錯誤: 創建虛擬環境失敗
        pause
        exit /b 1
    )
)

REM 激活虛擬環境
echo 激活虛擬環境...
call venv\Scripts\activate.bat

REM 升級 pip
echo 升級 pip...
python -m pip install --upgrade pip

REM 安裝依賴
if not exist "venv\Lib\site-packages\PyQt5" (
    echo 安裝依賴包...
    echo 這可能需要幾分鐘時間，請耐心等待...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 錯誤: 安裝依賴失敗
        echo 嘗試使用國內鏡像源...
        pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
        if errorlevel 1 (
            echo 安裝依賴仍然失敗，請檢查網絡連接
            pause
            exit /b 1
        )
    )
)

REM 啟動程序
echo.
echo 依賴安裝完成，啟動 python_GeekDesk_TPV...
echo.
python main.py

REM 如果程序異常退出，暫停以查看錯誤信息
if errorlevel 1 (
    echo.
    echo 程序異常退出，請檢查錯誤信息
    pause
)
