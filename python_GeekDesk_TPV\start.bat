@echo off
chcp 65001 >nul
title python_GeekDesk_TPV

echo.
echo ========================================
echo   python_GeekDesk_TPV 桌面效率工具
echo ========================================
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: 未找到 Python，請先安裝 Python 3.8 或更高版本
    echo 下載地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 檢查虛擬環境
if not exist "venv" (
    echo 創建虛擬環境...
    python -m venv venv
    if errorlevel 1 (
        echo 錯誤: 創建虛擬環境失敗
        pause
        exit /b 1
    )
)

REM 激活虛擬環境
echo 激活虛擬環境...
call venv\Scripts\activate.bat

REM 安裝依賴
if not exist "venv\Lib\site-packages\PyQt5" (
    echo 安裝依賴包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 錯誤: 安裝依賴失敗
        pause
        exit /b 1
    )
)

REM 啟動程序
echo 啟動 python_GeekDesk_TPV...
echo.
python main.py

REM 如果程序異常退出，暫停以查看錯誤信息
if errorlevel 1 (
    echo.
    echo 程序異常退出，請檢查錯誤信息
    pause
)
