# python_GeekDesk_TPV 開發文檔

## 專案結構

```
python_GeekDesk_TPV/
├── main.py                 # 主程序入口
├── requirements.txt        # 依賴列表
├── README.md              # 專案說明
├── constants/             # 常量定義
│   ├── __init__.py
│   ├── constants.py       # 應用常量
│   └── enums.py          # 枚舉類型
├── core/                  # 核心模塊
│   ├── __init__.py
│   └── app.py            # 主應用程序類
├── models/                # 數據模型
│   ├── __init__.py
│   ├── base.py           # 基礎模型
│   ├── menu_item.py      # 菜單項模型
│   └── todo_item.py      # 待辦事項模型
├── utils/                 # 工具模塊
│   ├── __init__.py
│   ├── logger.py         # 日誌管理
│   ├── config_manager.py # 配置管理
│   ├── hotkey_manager.py # 熱鍵管理
│   ├── tray_manager.py   # 托盤管理
│   └── search_manager.py # 搜索管理
├── views/                 # 視圖層
│   ├── __init__.py
│   ├── main_window.py    # 主窗口
│   ├── settings_window.py # 設置窗口
│   └── todo_window.py    # 待辦窗口
├── resources/             # 資源文件
│   ├── __init__.py
│   ├── icons/            # 圖標文件
│   └── images/           # 圖片文件
├── config/                # 配置文件
│   └── default_config.json
├── logs/                  # 日誌文件
├── data/                  # 數據文件
├── temp/                  # 臨時文件
└── backup/               # 備份文件
```

## 核心概念

### 1. 應用程序架構

python_GeekDesk_TPV 採用分層架構設計：

- **主程序層 (main.py)**: 應用程序入口，負責初始化和啟動
- **核心層 (core/)**: 主要業務邏輯和應用程序管理
- **模型層 (models/)**: 數據模型和業務實體
- **工具層 (utils/)**: 通用工具和管理器
- **視圖層 (views/)**: 用戶界面和交互
- **常量層 (constants/)**: 常量定義和枚舉

### 2. 配置管理

配置管理器 (`ConfigManager`) 負責：
- 加載和保存配置文件
- 提供配置項的讀寫接口
- 支持配置的導入導出
- 配置的備份和恢復

配置文件分為兩層：
- `default_config.json`: 默認配置
- `user_config.json`: 用戶自定義配置

### 3. 熱鍵管理

熱鍵管理器 (`HotkeyManager`) 功能：
- 註冊全局熱鍵
- 熱鍵衝突檢測
- 動態更新熱鍵
- 熱鍵的啟用/禁用

### 4. 搜索功能

搜索管理器 (`SearchManager`) 支持：
- 文件名搜索
- Everything 集成搜索
- 文件內容搜索
- 搜索結果排序和過濾

### 5. 系統托盤

托盤管理器 (`TrayManager`) 提供：
- 系統托盤圖標
- 右鍵菜單
- 通知消息
- 托盤圖標狀態管理

## 建立新 App

### 1. 創建新的視圖窗口

```python
# views/new_window.py
from PyQt5.QtWidgets import QDialog
from utils.config_manager import ConfigManager

class NewWindow(QDialog):
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self._init_ui()
    
    def _init_ui(self):
        # 初始化UI
        pass
```

### 2. 在主應用中註冊

```python
# core/app.py
def _init_windows(self):
    # 添加新窗口的初始化
    self.new_window = None

def show_new_window(self):
    if not self.new_window:
        self.new_window = NewWindow(self.config_manager)
    self.new_window.show()
```

### 3. 添加菜單項

```python
# 在托盤菜單中添加新項目
self.tray_manager.add_menu_action(
    "show_new",
    "新功能",
    callback=self.show_new_window
)
```

### 4. 添加熱鍵支持

```python
# 註冊新的熱鍵
self.hotkey_manager.register_hotkey(
    "new_window",
    "ctrl+shift+n",
    self.show_new_window
)
```

## 主要 python_GeekDesk_TPV 概念

### 1. 信號和槽機制

使用 PyQt5 的信號槽機制進行組件間通信：

```python
from PyQt5.QtCore import pyqtSignal

class MyWidget(QWidget):
    # 定義信號
    data_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        # 連接信號到槽
        self.data_changed.connect(self.on_data_changed)
    
    def on_data_changed(self, data):
        # 處理信號
        pass
```

### 2. 配置驅動的UI

UI 組件根據配置動態調整：

```python
def _apply_config(self):
    opacity = self.config_manager.get_setting("ui.opacity", 0.9)
    self.setWindowOpacity(opacity)
    
    theme = self.config_manager.get_setting("ui.theme", "auto")
    self._apply_theme(theme)
```

### 3. 異步搜索

搜索功能使用工作線程避免阻塞UI：

```python
class SearchWorker(QThread):
    results_ready = pyqtSignal(list)
    
    def run(self):
        # 執行搜索
        results = self._perform_search()
        self.results_ready.emit(results)
```

### 4. 資源管理

統一管理應用程序資源：

```python
from constants.constants import ICONS_DIR, IMAGES_DIR

def load_icon(name):
    icon_path = ICONS_DIR / f"{name}.png"
    if icon_path.exists():
        return QIcon(str(icon_path))
    return QIcon()  # 默認圖標
```

### 5. 錯誤處理和日誌

統一的錯誤處理和日誌記錄：

```python
import logging

class MyClass:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def some_method(self):
        try:
            # 執行操作
            pass
        except Exception as e:
            self.logger.error(f"操作失敗: {e}", exc_info=True)
```

## 開發指南

### 1. 代碼風格

- 遵循 PEP 8 代碼風格
- 使用類型提示
- 添加適當的文檔字符串
- 保持函數和類的單一職責

### 2. 測試

- 為新功能編寫單元測試
- 使用 pytest 作為測試框架
- 測試文件放在 `tests/` 目錄

### 3. 文檔

- 更新相關文檔
- 添加新功能的使用說明
- 保持 README.md 的更新

### 4. 版本控制

- 使用語義化版本號
- 編寫清晰的提交消息
- 創建功能分支進行開發

## 常見問題

### Q: 如何添加新的配置項？

A: 在 `default_config.json` 中添加默認值，然後在代碼中使用 `config_manager.get_setting()` 讀取。

### Q: 如何自定義界面主題？

A: 修改 UI 組件的樣式表，並在配置中添加主題選項。

### Q: 如何集成新的搜索引擎？

A: 在 `SearchManager` 中添加新的搜索類型和對應的搜索邏輯。

### Q: 如何添加新的熱鍵？

A: 使用 `HotkeyManager.register_hotkey()` 方法註冊新的熱鍵組合。

## 貢獻指南

1. Fork 專案
2. 創建功能分支
3. 編寫代碼和測試
4. 提交 Pull Request
5. 等待代碼審查

歡迎貢獻代碼、報告問題或提出改進建議！
