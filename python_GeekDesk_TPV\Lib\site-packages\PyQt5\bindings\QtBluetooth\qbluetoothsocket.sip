// qbluetoothsocket.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QBluetoothSocket : public QIODevice
{
%TypeHeaderCode
#include <qbluetoothsocket.h>
%End

public:
    enum SocketState
    {
        UnconnectedState,
        ServiceLookupState,
        ConnectingState,
        ConnectedState,
        BoundState,
        ClosingState,
        ListeningState,
    };

    enum SocketError
    {
        NoSocketError,
        UnknownSocketError,
        HostNotFoundError,
        ServiceNotFoundError,
        NetworkError,
        UnsupportedProtocolError,
%If (Qt_5_3_0 -)
        OperationError,
%End
%If (Qt_5_10_0 -)
        RemoteHostClosedError,
%End
    };

    QBluetoothSocket(QBluetoothServiceInfo::Protocol socketType, QObject *parent /TransferThis/ = 0);
%If (Qt_5_6_1 -)
    explicit QBluetoothSocket(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QBluetoothSocket(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QBluetoothSocket();
    void abort();
    virtual void close() /ReleaseGIL/;
    virtual bool isSequential() const;
    virtual qint64 bytesAvailable() const;
    virtual qint64 bytesToWrite() const;
    virtual bool canReadLine() const;
    void connectToService(const QBluetoothServiceInfo &service, QIODevice::OpenMode mode = QIODevice::ReadWrite) /ReleaseGIL/;
    void connectToService(const QBluetoothAddress &address, const QBluetoothUuid &uuid, QIODevice::OpenMode mode = QIODevice::ReadWrite) /ReleaseGIL/;
    void connectToService(const QBluetoothAddress &address, quint16 port, QIODevice::OpenMode mode = QIODevice::ReadWrite) /ReleaseGIL/;
    void disconnectFromService() /ReleaseGIL/;
    QString localName() const;
    QBluetoothAddress localAddress() const;
    quint16 localPort() const;
    QString peerName() const;
    QBluetoothAddress peerAddress() const;
    quint16 peerPort() const;
    bool setSocketDescriptor(int socketDescriptor, QBluetoothServiceInfo::Protocol socketType, QBluetoothSocket::SocketState state = QBluetoothSocket::ConnectedState, QIODevice::OpenMode mode = QIODevice::ReadWrite);
    int socketDescriptor() const;
    QBluetoothServiceInfo::Protocol socketType() const;
    QBluetoothSocket::SocketState state() const;
    QBluetoothSocket::SocketError error() const;
    QString errorString() const;

signals:
    void connected();
    void disconnected();
    void error(QBluetoothSocket::SocketError error);
    void stateChanged(QBluetoothSocket::SocketState state);

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxSize)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QBluetoothSocket::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 maxSize /ArraySize/) /ReleaseGIL/;
    void setSocketState(QBluetoothSocket::SocketState state);
    void setSocketError(QBluetoothSocket::SocketError error);
    void doDeviceDiscovery(const QBluetoothServiceInfo &service, QIODevice::OpenMode openMode);

public:
%If (Qt_5_6_0 -)
    void setPreferredSecurityFlags(QBluetooth::SecurityFlags flags);
%End
%If (Qt_5_6_0 -)
    QBluetooth::SecurityFlags preferredSecurityFlags() const;
%End
};

%End
