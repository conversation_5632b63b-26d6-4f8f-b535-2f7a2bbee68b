// qgeocoordinate.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QGeoCoordinate
{
%TypeHeaderCode
#include <qgeocoordinate.h>
%End

public:
    enum CoordinateType
    {
        InvalidCoordinate,
        Coordinate2D,
        Coordinate3D,
    };

    enum CoordinateFormat
    {
        Degrees,
        DegreesWithHemisphere,
        DegreesMinutes,
        DegreesMinutesWithHemisphere,
        DegreesMinutesSeconds,
        DegreesMinutesSecondsWithHemisphere,
    };

    QGeoCoordinate();
    QGeoCoordinate(double latitude, double longitude);
    QGeoCoordinate(double latitude, double longitude, double altitude);
    QGeoCoordinate(const QGeoCoordinate &other);
    ~QGeoCoordinate();
    bool operator==(const QGeoCoordinate &other) const;
    bool operator!=(const QGeoCoordinate &other) const;
    bool isValid() const;
    QGeoCoordinate::CoordinateType type() const;
    void setLatitude(double latitude);
    double latitude() const;
    void setLongitude(double longitude);
    double longitude() const;
    void setAltitude(double altitude);
    double altitude() const;
    qreal distanceTo(const QGeoCoordinate &other) const;
    qreal azimuthTo(const QGeoCoordinate &other) const;
    QGeoCoordinate atDistanceAndAzimuth(qreal distance, qreal azimuth, qreal distanceUp = 0.0) const;
    QString toString(QGeoCoordinate::CoordinateFormat format = QGeoCoordinate::DegreesMinutesSecondsWithHemisphere) const;
%If (Qt_5_7_0 -)
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
};

%End
%If (Qt_5_2_0 -)
QDataStream &operator<<(QDataStream &stream, const QGeoCoordinate &coordinate /Constrained/);
%End
%If (Qt_5_2_0 -)
QDataStream &operator>>(QDataStream &stream, QGeoCoordinate &coordinate /Constrained/);
%End
