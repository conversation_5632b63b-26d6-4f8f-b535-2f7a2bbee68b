// qjsengine.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qjsengine.h>
%End

class QJSEngine : public QObject
{
%TypeHeaderCode
#include <qjsengine.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QJSEngine, &sipType_QJSEngine, 8, 1},
        {sipName_QQmlComponent, &sipType_QQmlComponent, -1, 2},
        {sipName_QQmlContext, &sipType_QQmlContext, -1, 3},
    #if QT_VERSION >= 0x050f00
        {sipName_QQmlEngineExtensionPlugin, &sipType_QQmlEngineExtensionPlugin, -1, 4},
    #else
        {0, 0, -1, 4},
    #endif
        {sipName_QQmlExpression, &sipType_QQmlExpression, -1, 5},
        {sipName_QQmlExtensionPlugin, &sipType_QQmlExtensionPlugin, -1, 6},
    #if QT_VERSION >= 0x050200
        {sipName_QQmlFileSelector, &sipType_QQmlFileSelector, -1, 7},
    #else
        {0, 0, -1, 7},
    #endif
        {sipName_QQmlPropertyMap, &sipType_QQmlPropertyMap, -1, -1},
        {sipName_QQmlEngine, &sipType_QQmlEngine, 9, -1},
    #if QT_VERSION >= 0x050100
        {sipName_QQmlApplicationEngine, &sipType_QQmlApplicationEngine, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QJSEngine();
    explicit QJSEngine(QObject *parent /TransferThis/);
    virtual ~QJSEngine();
    QJSValue globalObject() const;
    QJSValue evaluate(const QString &program, const QString &fileName = QString(), int lineNumber = 1) /ReleaseGIL/;
    QJSValue newObject();
    QJSValue newArray(uint length = 0);
    QJSValue newQObject(QObject *object /Transfer/);
    void collectGarbage();
%If (Qt_5_4_0 -)
    void installTranslatorFunctions(const QJSValue &object = QJSValue());
%End
%If (Qt_5_6_0 -)

    enum Extension
    {
        TranslationExtension,
        ConsoleExtension,
        GarbageCollectionExtension,
        AllExtensions,
    };

%End
%If (Qt_5_6_0 -)
    typedef QFlags<QJSEngine::Extension> Extensions;
%End
%If (Qt_5_6_0 -)
    void installExtensions(QJSEngine::Extensions extensions, const QJSValue &object = QJSValue());
%End
%If (Qt_5_8_0 -)
    QJSValue newQMetaObject(const QMetaObject *metaObject);
%End
%If (Qt_5_12_0 -)
    QJSValue importModule(const QString &fileName);
%End
%If (Qt_5_12_0 -)
    QJSValue newErrorObject(QJSValue::ErrorType errorType, const QString &message = QString());
%End
%If (Qt_5_12_0 -)
    void throwError(const QString &message);
%End
%If (Qt_5_12_0 -)
    void throwError(QJSValue::ErrorType errorType, const QString &message = QString());
%End
%If (Qt_5_14_0 -)
    void setInterrupted(bool interrupted);
%End
%If (Qt_5_14_0 -)
    bool isInterrupted() const;
%End
%If (Qt_5_15_0 -)
    QString uiLanguage() const;
%End
%If (Qt_5_15_0 -)
    void setUiLanguage(const QString &language);
%End

signals:
%If (Qt_5_15_0 -)
    void uiLanguageChanged();
%End
};

%If (Qt_5_5_0 -)
QJSEngine *qjsEngine(const QObject *);
%End
%If (Qt_5_6_0 -)
QFlags<QJSEngine::Extension> operator|(QJSEngine::Extension f1, QFlags<QJSEngine::Extension> f2);
%End

%ModuleHeaderCode
#include "qpyqml_api.h"
%End

%PostInitialisationCode
qpyqml_post_init(sipModuleDict);
%End
