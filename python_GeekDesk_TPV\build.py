#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
python_GeekDesk_TPV 打包腳本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def clean_build_dirs():
    """清理構建目錄"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目錄: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理 .pyc 文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))


def build_executable():
    """構建可執行文件"""
    print("開始構建可執行文件...")
    
    # PyInstaller 命令
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包為單個文件
        '--windowed',                   # 無控制台窗口
        '--icon=resources/icons/logo.ico',  # 應用圖標
        '--name=GeekDesk_TPV',         # 輸出文件名
        '--add-data=config;config',     # 添加配置文件
        '--add-data=resources;resources',  # 添加資源文件
        '--hidden-import=PyQt5.sip',    # 隱藏導入
        '--hidden-import=keyboard',
        '--hidden-import=psutil',
        '--clean',                      # 清理臨時文件
        'main.py'                       # 主程序文件
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("構建成功！")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"構建失敗: {e}")
        print(f"錯誤輸出: {e.stderr}")
        return False
    
    return True


def create_installer():
    """創建安裝包"""
    print("創建安裝包...")
    
    # 這裡可以使用 NSIS 或其他工具創建安裝包
    # 暫時只是複製文件到發布目錄
    
    release_dir = Path("release")
    release_dir.mkdir(exist_ok=True)
    
    # 複製可執行文件
    exe_file = Path("dist/GeekDesk_TPV.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir / "GeekDesk_TPV.exe")
        print(f"可執行文件已複製到: {release_dir}")
    
    # 複製說明文件
    docs_to_copy = ["README.md", "LICENSE"]
    for doc in docs_to_copy:
        if os.path.exists(doc):
            shutil.copy2(doc, release_dir / doc)
    
    print("安裝包創建完成！")


def main():
    """主函數"""
    print("python_GeekDesk_TPV 構建腳本")
    print("=" * 50)
    
    # 檢查 Python 版本
    if sys.version_info < (3, 8):
        print("錯誤: 需要 Python 3.8 或更高版本")
        sys.exit(1)
    
    # 檢查依賴
    try:
        import PyQt5
        import pyinstaller
    except ImportError as e:
        print(f"錯誤: 缺少依賴 {e}")
        print("請運行: pip install -r requirements.txt")
        sys.exit(1)
    
    # 清理構建目錄
    clean_build_dirs()
    
    # 構建可執行文件
    if build_executable():
        # 創建安裝包
        create_installer()
        print("\n構建完成！")
        print("可執行文件位於: dist/GeekDesk_TPV.exe")
        print("發布文件位於: release/")
    else:
        print("\n構建失敗！")
        sys.exit(1)


if __name__ == "__main__":
    main()
