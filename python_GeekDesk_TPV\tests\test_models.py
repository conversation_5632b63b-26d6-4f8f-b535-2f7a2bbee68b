#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型測試
"""

import pytest
from datetime import datetime, timedelta

from models.menu_item import MenuItem
from models.todo_item import TodoItem
from constants.enums import MenuType, TodoTaskExecType


class TestMenuItem:
    """菜單項模型測試"""
    
    def test_create_menu_item(self):
        """測試創建菜單項"""
        item = MenuItem(
            name="測試應用",
            path="C:/test.exe",
            menu_type=MenuType.APPLICATION
        )
        
        assert item.name == "測試應用"
        assert item.path == "C:/test.exe"
        assert item.menu_type == MenuType.APPLICATION
        assert item.visible is True
        assert item.use_count == 0
    
    def test_menu_item_to_dict(self):
        """測試菜單項轉字典"""
        item = MenuItem(
            name="測試應用",
            path="C:/test.exe",
            description="測試描述"
        )
        
        data = item.to_dict()
        
        assert data['name'] == "測試應用"
        assert data['path'] == "C:/test.exe"
        assert data['description'] == "測試描述"
        assert 'id' in data
        assert 'created_at' in data
    
    def test_menu_item_from_dict(self):
        """測試從字典創建菜單項"""
        data = {
            'name': "測試應用",
            'path': "C:/test.exe",
            'description': "測試描述",
            'menu_type': MenuType.APPLICATION.value
        }
        
        item = MenuItem.from_dict(data)
        
        assert item.name == "測試應用"
        assert item.path == "C:/test.exe"
        assert item.description == "測試描述"
        assert item.menu_type == MenuType.APPLICATION
    
    def test_menu_item_tags(self):
        """測試菜單項標籤功能"""
        item = MenuItem(name="測試", path="test.exe")
        
        # 添加標籤
        item.add_tag("開發工具")
        item.add_tag("編輯器")
        
        assert item.has_tag("開發工具")
        assert item.has_tag("編輯器")
        assert len(item.tags) == 2
        
        # 移除標籤
        item.remove_tag("開發工具")
        assert not item.has_tag("開發工具")
        assert len(item.tags) == 1
    
    def test_menu_item_search(self):
        """測試菜單項搜索匹配"""
        item = MenuItem(
            name="Visual Studio Code",
            description="代碼編輯器",
            path="C:/vscode.exe"
        )
        item.add_tag("編輯器")
        
        # 測試名稱匹配
        assert item.matches_search("visual")
        assert item.matches_search("code")
        
        # 測試描述匹配
        assert item.matches_search("代碼")
        assert item.matches_search("編輯器")
        
        # 測試標籤匹配
        assert item.matches_search("編輯器")
        
        # 測試路徑匹配
        assert item.matches_search("vscode")
        
        # 測試不匹配
        assert not item.matches_search("photoshop")


class TestTodoItem:
    """待辦事項模型測試"""
    
    def test_create_todo_item(self):
        """測試創建待辦事項"""
        item = TodoItem(
            title="完成項目",
            content="需要在週五前完成",
            priority=4
        )
        
        assert item.title == "完成項目"
        assert item.content == "需要在週五前完成"
        assert item.priority == 4
        assert item.completed is False
        assert item.reminder_enabled is True
    
    def test_todo_item_completion(self):
        """測試待辦事項完成狀態"""
        item = TodoItem(title="測試任務")
        
        # 初始狀態
        assert not item.completed
        assert item.completed_at is None
        
        # 標記完成
        item.mark_completed()
        assert item.completed
        assert item.completed_at is not None
        
        # 標記未完成
        item.mark_uncompleted()
        assert not item.completed
        assert item.completed_at is None
    
    def test_todo_item_due_date(self):
        """測試待辦事項到期時間"""
        now = datetime.now()
        
        # 今天到期
        item_today = TodoItem(
            title="今天到期",
            due_date=now
        )
        assert item_today.is_due_today()
        assert not item_today.is_overdue()
        
        # 明天到期
        item_tomorrow = TodoItem(
            title="明天到期",
            due_date=now + timedelta(days=1)
        )
        assert not item_tomorrow.is_due_today()
        assert not item_tomorrow.is_overdue()
        assert item_tomorrow.is_due_soon(48)  # 48小時內
        
        # 已過期
        item_overdue = TodoItem(
            title="已過期",
            due_date=now - timedelta(days=1)
        )
        assert item_overdue.is_overdue()
        assert not item_overdue.is_due_today()
    
    def test_todo_item_reminder(self):
        """測試待辦事項提醒"""
        now = datetime.now()
        
        # 需要提醒
        item_remind = TodoItem(
            title="需要提醒",
            reminder_time=now - timedelta(minutes=1),
            reminder_enabled=True
        )
        assert item_remind.should_remind()
        
        # 不需要提醒（未到時間）
        item_not_time = TodoItem(
            title="未到時間",
            reminder_time=now + timedelta(hours=1),
            reminder_enabled=True
        )
        assert not item_not_time.should_remind()
        
        # 不需要提醒（已禁用）
        item_disabled = TodoItem(
            title="已禁用提醒",
            reminder_time=now - timedelta(minutes=1),
            reminder_enabled=False
        )
        assert not item_disabled.should_remind()
        
        # 不需要提醒（已完成）
        item_completed = TodoItem(
            title="已完成",
            reminder_time=now - timedelta(minutes=1),
            reminder_enabled=True,
            completed=True
        )
        assert not item_completed.should_remind()
    
    def test_todo_item_repeat(self):
        """測試待辦事項重複設置"""
        now = datetime.now()
        
        # 每日重複
        item_daily = TodoItem(
            title="每日任務",
            exec_type=TodoTaskExecType.DAILY,
            repeat_interval=1,
            last_reminded=now - timedelta(days=1, minutes=1)
        )
        
        next_time = item_daily.get_next_reminder_time()
        assert next_time is not None
        
        # 每週重複
        item_weekly = TodoItem(
            title="每週任務",
            exec_type=TodoTaskExecType.WEEKLY,
            repeat_interval=1
        )
        
        interval = item_weekly._get_repeat_interval_timedelta()
        assert interval.days == 7
    
    def test_todo_item_priority_text(self):
        """測試優先級文本"""
        item = TodoItem(title="測試", priority=5)
        assert item.get_priority_text() == "很高"
        
        item.priority = 1
        assert item.get_priority_text() == "很低"
        
        item.priority = 3
        assert item.get_priority_text() == "中"
    
    def test_todo_item_status_text(self):
        """測試狀態文本"""
        now = datetime.now()
        
        # 已完成
        item_completed = TodoItem(title="已完成", completed=True)
        assert item_completed.get_status_text() == "已完成"
        
        # 已過期
        item_overdue = TodoItem(
            title="已過期",
            due_date=now - timedelta(days=1)
        )
        assert item_overdue.get_status_text() == "已過期"
        
        # 今天到期
        item_today = TodoItem(
            title="今天到期",
            due_date=now
        )
        assert item_today.get_status_text() == "今天到期"
        
        # 即將到期
        item_soon = TodoItem(
            title="即將到期",
            due_date=now + timedelta(hours=12)
        )
        assert item_soon.get_status_text() == "即將到期"
        
        # 進行中
        item_progress = TodoItem(
            title="進行中",
            due_date=now + timedelta(days=7)
        )
        assert item_progress.get_status_text() == "進行中"


if __name__ == "__main__":
    pytest.main([__file__])
