{"app": {"name": "python_GeekDesk_TPV", "version": "1.0.0", "auto_start": false, "minimize_to_tray": true, "close_to_tray": true, "language": "zh_CN"}, "hotkeys": {"main": "ctrl+space", "todo": "ctrl+shift+t", "settings": "ctrl+shift+s"}, "ui": {"theme": "auto", "opacity": 0.9, "corner_radius": 10, "window_width": 800, "window_height": 600, "follow_mouse": true, "animation_enabled": true, "blur_background": true, "font_family": "Microsoft YaHei", "font_size": 10}, "search": {"max_results": 50, "search_delay": 300, "enable_everything": true, "enable_content_search": true, "search_history": true, "max_history": 20, "search_paths": ["~/Desktop", "~/Documents", "~/Downloads", "C:/Program Files", "C:/Program Files (x86)"]}, "todo": {"default_reminder": true, "reminder_sound": true, "reminder_popup": true, "auto_save": true, "reminder_interval": 60, "default_priority": 3}, "menu": {"show_icons": true, "icon_size": 32, "grid_columns": 6, "show_labels": true, "auto_arrange": true}, "logging": {"level": "INFO", "console_output": true, "file_output": true, "max_file_size": 10485760, "backup_count": 5}, "performance": {"cache_enabled": true, "cache_size": 100, "preload_icons": true, "lazy_loading": true}}