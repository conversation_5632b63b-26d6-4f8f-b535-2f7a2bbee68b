#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
常量定義模塊
"""

import os
from pathlib import Path

# 應用程序信息
APP_NAME = "python_GeekDesk_TPV"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Your Name"
APP_DESCRIPTION = "桌面效率工具"

# 開發模式
DEV_MODE = False

# 路徑常量
APP_DIR = Path(__file__).parent.parent
DATA_DIR = APP_DIR / "data"
CONFIG_DIR = APP_DIR / "config"
LOGS_DIR = APP_DIR / "logs"
RESOURCES_DIR = APP_DIR / "resources"
ICONS_DIR = RESOURCES_DIR / "icons"
IMAGES_DIR = RESOURCES_DIR / "images"
TEMP_DIR = APP_DIR / "temp"
BACKUP_DIR = APP_DIR / "backup"

# 確保目錄存在
for directory in [DATA_DIR, CONFIG_DIR, LOGS_DIR, RESOURCES_DIR, ICONS_DIR, IMAGES_DIR, TEMP_DIR, BACKUP_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# 配置文件路徑
CONFIG_FILE = CONFIG_DIR / "config.json"
USER_CONFIG_FILE = CONFIG_DIR / "user_config.json"
HOTKEY_CONFIG_FILE = CONFIG_DIR / "hotkeys.json"
MENU_CONFIG_FILE = CONFIG_DIR / "menu.json"

# 日誌文件路徑
LOG_FILE = LOGS_DIR / "geekdesk.log"
ERROR_LOG_FILE = LOGS_DIR / "error.log"

# 默認設置
DEFAULT_HOTKEY = "ctrl+space"
DEFAULT_TODO_HOTKEY = "ctrl+shift+t"
DEFAULT_OPACITY = 0.9
DEFAULT_CORNER_RADIUS = 10
DEFAULT_WINDOW_WIDTH = 800
DEFAULT_WINDOW_HEIGHT = 600

# 界面常量
SHADOW_WIDTH = 20
MIN_WINDOW_WIDTH = 400
MIN_WINDOW_HEIGHT = 300
MAX_WINDOW_WIDTH = 1920
MAX_WINDOW_HEIGHT = 1080

# 搜索設置
MAX_SEARCH_RESULTS = 50
SEARCH_DELAY_MS = 300

# 系統托盤
TRAY_ICON_SIZE = 16

# 文件類型圖標映射
FILE_TYPE_ICONS = {
    '.txt': 'text',
    '.doc': 'word',
    '.docx': 'word',
    '.xls': 'excel',
    '.xlsx': 'excel',
    '.ppt': 'powerpoint',
    '.pptx': 'powerpoint',
    '.pdf': 'pdf',
    '.jpg': 'image',
    '.jpeg': 'image',
    '.png': 'image',
    '.gif': 'image',
    '.bmp': 'image',
    '.mp3': 'audio',
    '.wav': 'audio',
    '.mp4': 'video',
    '.avi': 'video',
    '.mkv': 'video',
    '.zip': 'archive',
    '.rar': 'archive',
    '.7z': 'archive',
    '.exe': 'executable',
    '.msi': 'installer',
}

# 默認圖標
DEFAULT_FILE_ICON = "file"
DEFAULT_FOLDER_ICON = "folder"
DEFAULT_APP_ICON = "application"